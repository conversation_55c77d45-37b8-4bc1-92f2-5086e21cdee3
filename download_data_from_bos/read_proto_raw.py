#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
data distribution
Authors: <AUTHORS>
Date:    2025/03/18
"""
import os
import datetime
import logging
# from file_proc.parse_file import ParseFile
import auto_labeling_label_pb2
from pprint import pprint
from google.protobuf import text_format
import numpy as np
import time


def parse_obj(label_list, frame_id, frame_name):
    """
    直接解析数据并保存为 Parquet 文件
    :param label_list: 包含标签文件路径的列表
    :param output_path: 输出的 Parquet 文件路径（如 "output.parquet"）
    """
    # 存储所有解析后的对象数据
    all_objects = []
    
    for item in label_list:
        label_info = proto.auto_labeling_label_pb2.LabelInfo()
    
        try:
            # 尝试二进制解析
            with open(item, 'rb') as f:
                label_info.ParseFromString(f.read())
        except Exception as e:
            # print(f"Binary parse failed for {item}, trying text format: {e}")
            try:
                # 尝试文本格式解析
                with open(item, 'r') as f:
                    text_content = f.read()
                    label_info = text_format.Parse(text_content, proto.auto_labeling_label_pb2.LabelInfo())
            except Exception as e:
                print(f"Text parse also failed for {item}: {e}")
                continue  # 彻底失败则跳过
        
        if not label_info.HasField("auto_labeling_label"):
            continue
            
        auto_labeling_label = label_info.auto_labeling_label
        for obj in auto_labeling_label.lidar_label[0].objects:
            # 处理可见性标签
            try:
                visible_tag = int(obj.lidar_supplement.lidar_camera_info[0].visible)
            except Exception:
                visible_tag = -1
            
            # 处理方向角计算
            yaw, ry = None, None
            if obj.HasField("direction"):
                dx, dy, dz = obj.direction.data[0], obj.direction.data[1], obj.direction.data[2]
                yaw = np.arctan2(dy, dx)
                yaw = np.round(yaw - np.floor(yaw / (2 * np.pi) + 0.5) * (2 * np.pi), 8)
                ry = -yaw - np.pi / 2
                ry = ry - np.floor(ry / (2 * np.pi) + 0.5) * (2 * np.pi)
            
            # 处理相机视野标志
            valid_flag = False
            if obj.HasField("lidar_supplement"):
                lidar_cam_infos = obj.lidar_supplement.lidar_camera_info
                valid_flag = any(cam.visible for cam in lidar_cam_infos)
            
            # 直接构建字典并添加到列表
            all_objects.append({
                "frame_id": frame_id,
                "frame_name": frame_name,
                "id": obj.id,
                "uid": obj.uid,
                "type": obj.type,
                "sub_type": obj.sub_type,
                "track_uid": obj.track_uid,
                "sensor_name": obj.sensor_name,
                "confidence": obj.confidence,
                "box_x": obj.box.x if obj.HasField("box") else None,
                "box_y": obj.box.y if obj.HasField("box") else None,
                "box_w": obj.box.w if obj.HasField("box") else None,
                "box_h": obj.box.h if obj.HasField("box") else None,
                "center_x": obj.center.data[0] if obj.HasField("center") else None,
                "center_y": obj.center.data[1] if obj.HasField("center") else None,
                "center_z": obj.center.data[2] if obj.HasField("center") else None,
                "size_x": obj.size.data[0] if obj.HasField("size") else None,
                "size_y": obj.size.data[1] if obj.HasField("size") else None,
                "size_z": obj.size.data[2] if obj.HasField("size") else None,
                "direction_x": obj.direction.data[0] if obj.HasField("direction") else None,
                "direction_y": obj.direction.data[1] if obj.HasField("direction") else None,
                "direction_z": obj.direction.data[2] if obj.HasField("direction") else None,
                "velocity_x": obj.velocity.data[0] if obj.HasField("velocity") else None,
                "velocity_y": obj.velocity.data[1] if obj.HasField("velocity") else None,
                "velocity_z": obj.velocity.data[2] if obj.HasField("velocity") else None,
                "roi_state": obj.roi_state,
                "user_roi_state": obj.user_roi_state,
                "motion_state": obj.motion_state,
                "velocity_confidence": obj.velocity_confidence,
                "crispness_score": obj.crispness_score,
                "pts_count_inside": obj.pts_count_inside,
                "track_id": obj.track_id,
                "label_src": obj.label_src,
                "visible_tag": visible_tag,
                "yaw": yaw,
                "ry": ry,
                "valid_flag": valid_flag
            })
    
    # 直接转换为 DataFrame 并保存为 Parquet
    if all_objects:
        return all_objects
    else:
        return []

def parse_car_lidar(label_list):
    """
    解析主车雷达信息
    """
    objs = []
    for item in label_list:
        label_info = auto_labeling_label_pb2.LabelInfo()
        try: # text_format
            with open(item, 'rb') as f:
                label_info.ParseFromString(f.read())
        except:
            pass
        lidar_info = []
        # 检查并访问 auto_labeling_label
        if label_info.HasField("auto_labeling_label"):
            auto_labeling_label = label_info.auto_labeling_label
            lidar = auto_labeling_label.lidar_label[0].lidar2world_pose
            print(lidar.translation.x, lidar.translation.y, lidar.translation.z)
            lidar_info = [lidar.translation.x, lidar.translation.y, lidar.translation.z]
            lidar_info = [round(num, 2) for num in lidar_info]
        return lidar_info

# def parse_car_lidar(label_list):
#     """
#     解析主车雷达信息，包括平移参数和四元数旋转参数，并将它们合并到一个列表中
#     """
#     objs = []
#     for item in label_list:
#         label_info = auto_labeling_label_pb2.LabelInfo()
#         try:  # text_format
#             with open(item, 'rb') as f:
#                 label_info.ParseFromString(f.read())
#         except Exception as e:
#             print(f"Error parsing file {item}: {e}")
#             continue

#         lidar_data = []
#         # 检查并访问 auto_labeling_label
#         if label_info.HasField("auto_labeling_label"):
#             auto_labeling_label = label_info.auto_labeling_label
#             lidar = auto_labeling_label.lidar_label[0].lidar2world_pose

#             # 解析平移参数
#             translation = [lidar.translation.x, lidar.translation.y, lidar.translation.z]
#             translation = [round(num, 2) for num in translation]

#             # 解析四元数旋转参数
#             quaternion = [
#                 round(lidar.quaternion.qx, 6),
#                 round(lidar.quaternion.qy, 6),
#                 round(lidar.quaternion.qz, 6),
#                 round(lidar.quaternion.qw, 6)
#             ]

#             # 合并平移和四元数到一个列表
#             lidar_data = translation + quaternion

#         return lidar_data
 
 
if __name__ == '__main__':
    start_time = time.time()
    path = ["/home/<USER>/local_projects/BAIDU_TOOLS_QSZ/auto_labeling_gt.prototxt"]
    res = parse_car_lidar(path)
    pprint(res)
    end_time = time.time()
    print(end_time - start_time)

