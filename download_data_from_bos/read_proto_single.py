#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
data distribution
Authors: <AUTHORS>
Date:    2025/03/18
"""
import os
import datetime
import logging
# from file_proc.parse_file import ParseFile
import auto_labeling_label_pb2
from pprint import pprint
from google.protobuf import text_format
import numpy as np
import time


def parse_car_lidar(label_list):
    """
    解析主车雷达信息
    """
    for item in label_list:
        label_info = auto_labeling_label_pb2.LabelInfo()
        try:
            # 读取并解析 .prototxt 文件，忽略未知字段
            with open(item, 'r') as f:
                text_format.Merge(f.read(), label_info, allow_unknown_field=True)
        except Exception as e:
            print(f"Error parsing file {item}: {e}")
            continue

        lidar_info = []  # 存储lidar信息
        # 检查并访问 auto_labeling_label
        if label_info.HasField("auto_labeling_label"):
            auto_labeling_label = label_info.auto_labeling_label
            # 确保 lidar_label 存在并非空
            if auto_labeling_label.lidar_label:
                lidar = auto_labeling_label.lidar_label[0].lidar2world_pose
                # 提取并打印 lidar2world_pose 的 translation 信息
                lidar_info = [lidar.translation.x, lidar.translation.y, lidar.translation.z]
                lidar_info = [round(num, 2) for num in lidar_info]  # 保留两位小数

                quaternion = [
                    round(lidar.quaternion.qx, 6),
                    round(lidar.quaternion.qy, 6),
                    round(lidar.quaternion.qz, 6),
                    round(lidar.quaternion.qw, 6)
                ]
                lidar_info.extend(quaternion)
    return lidar_info


if __name__ == '__main__':
    start_time = time.time()
    path = ["/home/<USER>/local_projects/BAIDU_TOOLS_QSZ/auto_labeling_gt.prototxt"]  # 文件路径
    res = parse_car_lidar(path)
    end_time = time.time()
    print(f"Execution Time: {end_time - start_time:.2f} seconds")

