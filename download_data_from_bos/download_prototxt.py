import json
import os

# 定义 JSON 文件路径
json_file_path = "rt6_wuhan_motion_switch.json"
# 定义存储文件的目标目录
download_dir = "./wuhan_motion_switch"

# 确保目标目录存在
if not os.path.exists(download_dir):
    os.makedirs(download_dir)

# 定义下载命令模板
download_command_template = (
    "bcecmd bos cp -ry bos:/{bucket_name}/auto-labeling/label/{task_name}/{file_name}/labels/auto_labeling_gt.prototxt " + download_dir
)

# 加载 JSON 文件
try:
    with open(json_file_path, "r") as file:
        data = json.load(file)
except FileNotFoundError:
    print(f"错误: 找不到文件 {json_file_path}")
    exit(1)
except json.JSONDecodeError as e:
    print(f"错误: 解析 JSON 文件失败 - {e}")
    exit(1)

# 遍历 JSON 数据并执行下载操作
for index, (key, value) in enumerate(data.items()):
    try:
        bucket_name = value["bucket_name"]
        task_name = value["task_name"]
        file_name = value["file_name"]
    except KeyError as e:
        print(f"错误: JSON 数据中缺少关键字段 - {e}")
        continue

    # 拼接下载命令
    download_command = download_command_template.format(
        bucket_name=bucket_name,
        task_name=task_name,
        file_name=file_name
    )

    # 定义下载后的文件名（保留后缀）
    original_file_name = os.path.join(download_dir, "auto_labeling_gt.prototxt")
    new_file_name = os.path.join(download_dir, f"{os.path.splitext(key)[0]}.prototxt")  # 去掉 .json 后缀并添加 .prototxt

    # 执行下载命令
    print(f"正在下载文件 {key}...")
    os.system(download_command)  # 执行命令

    # 重命名下载的文件
    if os.path.exists(original_file_name):
        os.rename(original_file_name, new_file_name)
        print(f"文件下载并重命名为 {new_file_name}")
    else:
        print(f"警告: 文件 {original_file_name} 未找到，可能下载失败")

print("所有文件处理完成！")