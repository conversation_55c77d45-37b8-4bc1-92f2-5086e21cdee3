#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
data distribution
Authors: <AUTHORS>
Date:    2025/03/18
"""
import os
import time
import auto_labeling_label_pb2
from google.protobuf import text_format
from tqdm import tqdm
import pathos.multiprocessing


def parse_single_file(item_output):
    """
    解析单个 .prototxt 文件中的主车雷达信息
    :param item_output: 一个包含文件路径和输出目录的元组 (item, output_dir)
    :return: 保存结果的文件路径或错误消息
    """
    item, output_dir = item_output
    label_info = auto_labeling_label_pb2.LabelInfo()
    lidar_info = []

    try:
        # 读取并解析 .prototxt 文件，忽略未知字段
        with open(item, 'r') as f:
            text_format.Merge(f.read(), label_info, allow_unknown_field=True)

        # 检查并访问 auto_labeling_label
        if label_info.Has<PERSON>ield("auto_labeling_label"):
            auto_labeling_label = label_info.auto_labeling_label
            # 确保 lidar_label 存在并非空
            if auto_labeling_label.lidar_label:
                lidar = auto_labeling_label.lidar_label[0].lidar2world_pose
                # 提取 lidar2world_pose 的 translation 信息
                lidar_info = [
                    round(lidar.translation.x, 2),
                    round(lidar.translation.y, 2),
                    round(lidar.translation.z, 2),
                    round(lidar.quaternion.qx, 6),
                    round(lidar.quaternion.qy, 6),
                    round(lidar.quaternion.qz, 6),
                    round(lidar.quaternion.qw, 6)
                ]

        # 如果提取到 lidar_info，则保存到文件
        if lidar_info:
            # 提取文件名 (不包含路径和后缀)
            file_name = os.path.splitext(os.path.basename(item))[0]
            # 输出文件路径
            output_file = os.path.join(output_dir, f"{file_name}.txt")
            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)
            # 保存到文件
            with open(output_file, 'w') as f:
                f.write(" ".join(map(str, lidar_info)))
            return f"Saved lidar pose to {output_file}"
        else:
            return f"No lidar info found in {item}"

    except Exception as e:
        return f"Error parsing file {item}: {e}"


def multi_process(func, args_flow, num_process):
    """
    多进程处理
    :param func: 要运行的函数
    :param args_flow: 参数列表
    :param num_process: 进程数
    :return: 所有进程的运行结果
    """
    with pathos.multiprocessing.Pool(processes=num_process) as pool:
        with tqdm(total=len(args_flow), desc='Processing files...') as pbar:
            results = []
            for result in pool.imap_unordered(func, args_flow):
                results.append(result)
                pbar.update(1)
    return results


if __name__ == '__main__':
    start_time = time.time()

    # 输入和输出目录路径
    input_dir = "/home/<USER>/local_projects/BAIDU_TOOLS_QSZ/download_data_from_bos/raw_data_roujiche"
    output_dir = "/home/<USER>/local_projects/BAIDU_TOOLS_QSZ/download_data_from_bos/lidar2world_pose"

    # 获取所有 .prototxt 文件路径
    prototxt_files = [
        os.path.join(input_dir, file) for file in os.listdir(input_dir) if file.endswith(".prototxt")
    ]

    # 为每个文件构建参数 (文件路径, 输出目录)
    args_flow = [(file, output_dir) for file in prototxt_files]

    # 设置进程数
    num_process = 16  # 根据机器性能选择合适的进程数

    # 调用多进程函数
    results = multi_process(parse_single_file, args_flow, num_process)

    # 打印所有结果
    for res in results:
        print(res)

    end_time = time.time()
    print(f"Execution Time: {end_time - start_time:.2f} seconds")