import json
import math
import numpy as np

def extract_real_vel_results(json_filename):
    """加载 JSON 文件"""
    with open(json_filename, 'r') as file:
        json_data = json.load(file)
        return json_data

def quaternion_multiply(q1, q2):
    """四元数乘法 (x, y, z, w) 格式"""
    x1, y1, z1, w1 = q1
    x2, y2, z2, w2 = q2
    w = w1 * w2 - x1 * x2 - y1 * y2 - z1 * z2
    x = w1 * x2 + x1 * w2 + y1 * z2 - z1 * y2
    y = w1 * y2 - x1 * z2 + y1 * w2 + z1 * x2
    z = w1 * z2 + x1 * y2 - y1 * x2 + z1 * w2
    return (x, y, z, w)

def quaternion_conjugate(q):
    """计算四元数的共轭 (x, y, z, w) 格式"""
    x, y, z, w = q
    return (-x, -y, -z, w)

def quaternion_to_euler(q):
    """将四元数 (x, y, z, w) 转换为欧拉角 (roll, pitch, yaw)"""
    x, y, z, w = q
    # 计算 roll (绕 X 轴旋转)
    sinr_cosp = 2 * (w * x + y * z)
    cosr_cosp = 1 - 2 * (x * x + y * y)
    roll = math.atan2(sinr_cosp, cosr_cosp)

    # 计算 pitch (绕 Y 轴旋转)
    sinp = 2 * (w * y - z * x)
    if abs(sinp) >= 1:
        pitch = math.copysign(math.pi / 2, sinp)  # 使用 90 度代替
    else:
        pitch = math.asin(sinp)

    # 计算 yaw (绕 Z 轴旋转)
    siny_cosp = 2 * (w * z + x * y)
    cosy_cosp = 1 - 2 * (y * y + z * z)
    yaw = math.atan2(siny_cosp, cosy_cosp)

    return roll, pitch, yaw

def calculate_relative_orientation(ego_orientation, target_orientation):
    """计算目标车相对于自车的相对朝向 (roll, pitch, yaw)"""
    # 自车四元数的共轭
    ego_orientation_conj = quaternion_conjugate(ego_orientation)
    # 计算相对旋转四元数
    relative_orientation = quaternion_multiply(ego_orientation_conj, target_orientation)
    # 将相对四元数转换为欧拉角
    roll, pitch, yaw = quaternion_to_euler(relative_orientation)
    return roll, pitch, yaw

def match_and_calculate_direction(real_vel_ego_json, real_vel_target_json, time_threshold=0.015):
    """匹配时间戳并计算相对朝向 (roll, pitch, yaw)"""
    matched_results = []

    for target_entry in real_vel_target_json:
        target_time, target_data = target_entry
        closest_ego_entry = None
        min_time_diff = float('inf')

        # 找到与目标时间戳最接近的 ego 时间戳
        for ego_entry in real_vel_ego_json:
            ego_time, ego_data = ego_entry
            time_diff = abs(target_time - ego_time)
            if time_diff < min_time_diff and time_diff <= time_threshold:
                min_time_diff = time_diff
                closest_ego_entry = ego_entry

        # 如果找到匹配的时间戳，计算相对朝向
        if closest_ego_entry:
            _, ego_data = closest_ego_entry
            relative_roll, relative_pitch, relative_yaw = calculate_relative_orientation(
                ego_data["orientation"], target_data["orientation"]
            )
            # 添加相对朝向到目标车数据
            target_data["direction_rel"] = {
                "roll": relative_roll,
                "pitch": relative_pitch,
                "yaw": relative_yaw
            }
            matched_results.append([target_time, target_data])

    return matched_results