import numpy as np

# ────────────────────────────────────────────────
# ① 已知量（把你的实测数据替换到下面即可）
imu_center = np.array([-11.89344102,  -0.26109365,  -1.30420018], dtype=float)

# imu_direction 一般是 IMU 坐标系的 +X 轴在 ego 中的方向
imu_direction = np.array([ 0.999910827772391,
                           0.011319641651271596,
                          -0.007085352243834098 ], dtype=float)

body2imu_transform_matrix = np.array([
    [ 9.99845015e-01,  1.76050107e-02,  9.30284951e-05,  1.32450000e+00],
    [-1.76052564e-02,  9.99831057e-01,  5.28331282e-03, -5.50000000e-02],
    [ 6.39550533e-16, -5.28413178e-03,  9.99986039e-01,  3.77000000e-01],
    [ 0.00000000e+00,  0.00000000e+00,  0.00000000e+00,  1.00000000e+00]
])
# ────────────────────────────────────────────────

# ② 根据 imu_direction 构造 IMU 坐标系在 ego 中的 3×3 旋转矩阵
def build_rotation_from_x(world_x):
    """给定本地 +X 轴在世界坐标中的方向，生成一个右手旋转矩阵"""
    x_axis = world_x / np.linalg.norm(world_x)

    world_up = np.array([0., 0., 1.])
    # 若 x 与 up 共线，用另一个 up 向量避免退化
    if abs(np.dot(x_axis, world_up)) > 0.999:
        world_up = np.array([0., 1., 0.])

    y_axis = np.cross(world_up, x_axis)
    y_axis /= np.linalg.norm(y_axis)

    z_axis = np.cross(x_axis, y_axis)

    # 列向量就是 IMU 坐标系 (x,y,z) 轴在世界(ego)中的方向
    return np.column_stack((x_axis, y_axis, z_axis))

R_imu2ego = build_rotation_from_x(imu_direction)

# ③ 组装 4×4 的齐次矩阵 T_imu2ego
T_imu2ego = np.eye(4)
T_imu2ego[:3, :3] = R_imu2ego
T_imu2ego[:3, 3]  = imu_center

# ④ ego ← imu ← body ，连乘得到 ego ← body
T_body2ego = T_imu2ego @ body2imu_transform_matrix

# ⑤ 目标 C（body 坐标系原点）在 ego 中的 3D 位置
C_pos_ego = T_body2ego[:3, 3]

print("Target-C position in ego frame:", C_pos_ego)