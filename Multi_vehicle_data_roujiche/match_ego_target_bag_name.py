import re
from datetime import datetime
import pandas as pd

def extract_timestamp_from_name(name: str):
    """从数据包名称中提取时间戳并转换为datetime对象"""
    match = re.search(r'_(\d{10})_(\d{10})$', name)
    if match:
        start_ts = int(match.group(1))
        return datetime.fromtimestamp(start_ts)
    return None

def load_packets(file_path: str):
    """读取txt文件，返回 (包名, 时间戳) 列表"""
    packets = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            name = line.strip()
            if not name:
                continue
            dt = extract_timestamp_from_name(name)
            if dt:
                packets.append((name, dt))
    return packets

def find_closest_matches(main_list, target_list):
    """为每个主车包找到最近的目标车包，过滤掉时间差为XXX秒的记录"""
    result = []
    for main_name, main_time in main_list:
        closest = min(target_list, key=lambda x: abs((x[1] - main_time).total_seconds()))
        time_diff = abs((closest[1] - main_time).total_seconds())
        if time_diff < 6.0:
            result.append({
                "主车包": main_name,
                "目标车包": closest[0],
                "主车时间": main_time,
                "目标车时间": closest[1],
                "时间差（秒）": time_diff
            })
    return sorted(result, key=lambda x: x["主车时间"])

if __name__ == "__main__":
    main_file = "./multi_velhicle_bag_name/ego_bag_切车绕八字.txt"
    target_file = "./multi_velhicle_bag_name/target_bag_all.txt"

    main_packets = load_packets(main_file)
    target_packets = load_packets(target_file)

    matches = find_closest_matches(main_packets, target_packets)

    # 导出为 Excel 文件
    df = pd.DataFrame(matches)
    df.to_excel("./multi_velhicle_bag_name/matched_packets.xlsx", index=False)
    print("匹配结果已保存为 matched_packets.xlsx")

    # 提取所有匹配到的目标车包名
    target_names = [match["目标车包"] for match in matches]

    # 保存目标车包名到一个新的 txt 文件
    target_txt_path = "./multi_velhicle_bag_name/target_bag_matched.txt"
    with open(target_txt_path, 'w', encoding='utf-8') as f:
        for target_name in target_names:
            f.write(target_name + '\n')
    
    print(f"目标车包名已保存为 {target_txt_path}")