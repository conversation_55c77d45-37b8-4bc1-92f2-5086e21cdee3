import json
import math
import numpy as np

def extract_real_vel_results(json_filename):
    """加载 JSON 文件"""
    with open(json_filename, 'r') as file:
        json_data = json.load(file)
        return json_data

def quaternion_multiply(q1, q2):
    """四元数乘法 (x, y, z, w) 格式"""
    x1, y1, z1, w1 = q1
    x2, y2, z2, w2 = q2
    w = w1 * w2 - x1 * x2 - y1 * y2 - z1 * z2
    x = w1 * x2 + x1 * w2 + y1 * z2 - z1 * y2
    y = w1 * y2 - x1 * z2 + y1 * w2 + z1 * x2
    z = w1 * z2 + x1 * y2 - y1 * x2 + z1 * w2
    return (x, y, z, w)

def quaternion_conjugate(q):
    """计算四元数的共轭 (x, y, z, w) 格式"""
    x, y, z, w = q
    return (-x, -y, -z, w)

def quaternion_rotate_vector(q, v):
    """
    使用四元数旋转向量 (x, y, z)。
    q: 四元数 (x, y, z, w)
    v: 三维向量 (x, y, z)
    返回旋转后的三维向量。
    """
    # 将向量表示为纯四元数 (x, y, z, 0)
    v_quat = (v[0], v[1], v[2], 0)
    # 计算旋转后的向量四元数
    q_conj = quaternion_conjugate(q)
    rotated_v_quat = quaternion_multiply(quaternion_multiply(q, v_quat), q_conj)
    # 返回旋转后的三维向量部分
    return rotated_v_quat[:3]

def matrix_to_quaternion(matrix):
    """
    将旋转矩阵转换为四元数 (x, y, z, w)。
    matrix: 3x3 的旋转矩阵
    """
    m = matrix
    trace = m[0][0] + m[1][1] + m[2][2]
    if trace > 0:
        s = 0.5 / math.sqrt(trace + 1.0)
        w = 0.25 / s
        x = (m[2][1] - m[1][2]) * s
        y = (m[0][2] - m[2][0]) * s
        z = (m[1][0] - m[0][1]) * s
    elif m[0][0] > m[1][1] and m[0][0] > m[2][2]:
        s = 2.0 * math.sqrt(1.0 + m[0][0] - m[1][1] - m[2][2])
        w = (m[2][1] - m[1][2]) / s
        x = 0.25 * s
        y = (m[0][1] + m[1][0]) / s
        z = (m[0][2] + m[2][0]) / s
    elif m[1][1] > m[2][2]:
        s = 2.0 * math.sqrt(1.0 + m[1][1] - m[0][0] - m[2][2])
        w = (m[0][2] - m[2][0]) / s
        x = (m[0][1] + m[1][0]) / s
        y = 0.25 * s
        z = (m[1][2] + m[2][1]) / s
    else:
        s = 2.0 * math.sqrt(1.0 + m[2][2] - m[0][0] - m[1][1])
        w = (m[1][0] - m[0][1]) / s
        x = (m[0][2] + m[2][0]) / s
        y = (m[1][2] + m[2][1]) / s
        z = 0.25 * s
    return (x, y, z, w)

def lidar_to_world_orientation(lidar_to_imu, imu_orientation):
    """
    计算激光雷达坐标系到世界坐标系的旋转四元数。
    lidar_to_imu: 4x4 的齐次变换矩阵
    imu_orientation: IMU 坐标系下的四元数
    """
    # 提取 lidar_to_imu 的旋转部分
    lidar_to_imu_rotation = np.array(lidar_to_imu)[:3, :3]
    lidar_to_imu_quaternion = matrix_to_quaternion(lidar_to_imu_rotation)

    # 计算激光雷达到世界的旋转四元数
    return quaternion_multiply(imu_orientation, lidar_to_imu_quaternion)

def calculate_relative_orientation_vector(ego_data, target_data):
    """计算目标车相对于自车的朝向，返回单位向量形式"""

    # 计算自车和目标车在激光雷达坐标系下的四元数
    ego_lidar_orientation = lidar_to_world_orientation(
        ego_data["lidar_to_imu"], ego_data["orientation"]
    )
    target_lidar_orientation = lidar_to_world_orientation(
        target_data["lidar_to_imu"], target_data["orientation"]
    )

    # 自车四元数的共轭
    ego_lidar_orientation_conj = quaternion_conjugate(ego_lidar_orientation)

    # 计算相对旋转四元数
    relative_orientation = quaternion_multiply(
        ego_lidar_orientation_conj, target_lidar_orientation
    )

    # 定义参考方向 (1, 0, 0) 表示目标车前向
    reference_vector = (1, 0, 0)

    # 计算相对朝向的单位向量
    relative_direction = quaternion_rotate_vector(relative_orientation, reference_vector)
    return relative_direction


def match_and_calculate_direction_vector(real_vel_ego_json, real_vel_target_json, time_threshold=0.015):
    """匹配时间戳并计算相对朝向 (单位向量形式)"""
    matched_results = []

    for target_entry in real_vel_target_json:
        target_time, target_data = target_entry
        closest_ego_entry = None
        min_time_diff = float('inf')

        # 找到与目标时间戳最接近的 ego 时间戳
        for ego_entry in real_vel_ego_json:
            ego_time, ego_data = ego_entry
            time_diff = abs(target_time - ego_time)
            if time_diff < min_time_diff and time_diff <= time_threshold:
                min_time_diff = time_diff
                closest_ego_entry = ego_entry

        # 如果找到匹配的时间戳，计算相对朝向
        if closest_ego_entry:
            _, ego_data = closest_ego_entry
            relative_direction = calculate_relative_orientation_vector(ego_data, target_data)
            # 添加相对朝向到目标车数据
            target_data["direction_rel_vector"] = {
                "x": relative_direction[0],
                "y": relative_direction[1],
                "z": relative_direction[2]
            }
            matched_results.append([target_time, target_data])
    return matched_results