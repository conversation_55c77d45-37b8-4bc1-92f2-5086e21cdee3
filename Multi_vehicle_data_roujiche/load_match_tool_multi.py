import os
import math
import pdb
import numpy as np
import json
from google.protobuf import text_format
import auto_labeling_label_pb2
import matplotlib.pyplot as plt
from matplotlib.ticker import AutoLocator
from pathlib import Path
from cal_target2ego_rel_direction_v2 import match_and_calculate_direction_vector
from utils import *


def is_nan(value):
    """检查值是否为nan"""
    try:
        return math.isnan(float(value))
    except:
        return False

def quaternion_to_rotation_matrix(q):
    """将四元数转换为3x3旋转矩阵"""
    qx, qy, qz, qw = q['qx'], q['qy'], q['qz'], q['qw']
    norm = math.sqrt(qx*qx + qy*qy + qz*qz + qw*qw)
    if norm == 0:
        return np.identity(3)
    qx, qy, qz, qw = qx/norm, qy/norm, qz/norm, qw/norm
    return np.array([
        [1 - 2*qy**2 - 2*qz**2,     2*qx*qy - 2*qz*qw,     2*qx*qz + 2*qy*qw],
        [2*qx*qy + 2*qz*qw,     1 - 2*qx**2 - 2*qz**2,     2*qy*qz - 2*qx*qw],
        [2*qx*qz - 2*qy*qw,     2*qy*qz + 2*qx*qw,     1 - 2*qx**2 - 2*qy**2]
    ])

def pose_to_transform_matrix(translation, quaternion):
    """将位姿转换为4x4齐次变换矩阵"""
    transform = np.identity(4)
    if not is_nan(translation['x']):
        transform[0, 3] = translation['x']
    if not is_nan(translation['y']):
        transform[1, 3] = translation['y']
    if not is_nan(translation['z']):
        transform[2, 3] = translation['z']
    rot_matrix = quaternion_to_rotation_matrix(quaternion)
    transform[:3, :3] = rot_matrix
    
    return transform

def transform_world_to_lidar(center, transform_matrix):
    """
    将中心点从世界坐标系转换到激光雷达坐标系。

    参数:
        center (list or np.array): 世界坐标系中的中心点 [x, y, z]
        transform_matrix (np.array): 激光雷达到世界的变换矩阵 (4x4)

    返回:
        np.array: 激光雷达坐标系中的中心点 [x', y', z']
    """
    if len(center) != 3:
        raise ValueError("center 参数必须是长度为 3 的列表或数组 [x, y, z]")
    
    # 转换为齐次坐标 [x, y, z, 1]
    center_world_homogeneous = np.append(center, 1)
    
    # 计算世界到激光雷达的逆变换矩阵
    world2lidar_transform_matrix = np.linalg.inv(transform_matrix)
    
    # 应用变换矩阵，将点从世界坐标系转换到激光雷达坐标系
    center_lidar_homogeneous = np.dot(world2lidar_transform_matrix, center_world_homogeneous)
    
    # 返回结果，去掉齐次坐标的最后一位
    return center_lidar_homogeneous[:3]


def save_proto_to_text_file(auto_label_match_info, message, file_path, label_proto_data, transform_matrix):
    """将protobuf消息保存为文本格式文件"""
    track_id = auto_label_match_info['track_id']
    new_obj = message.auto_labeling_label.lidar_label[0].objects.add()
    new_obj.label_src = "gt_vel"
    new_obj.track_id = track_id
    new_obj.center.data.extend([auto_label_match_info['center'][0], auto_label_match_info['center'][1], auto_label_match_info['center'][2]])
    new_obj.velocity.data.extend([auto_label_match_info['velocity_rel'][0], auto_label_match_info['velocity_rel'][1], auto_label_match_info['velocity_rel'][2]])
    new_obj.acceleration.data.extend([auto_label_match_info['acc'][0], auto_label_match_info['acc'][1], auto_label_match_info['acc'][2]])
    new_obj.yaw_rate = auto_label_match_info['angular_velocity'][2]
    new_obj.front_wheel_steer_angle = float(auto_label_match_info['turn_angle'])
    new_obj.vel_dir_diff_angle = float(auto_label_match_info['vel_dir_diff_angle'])

    # direction_dict = auto_label_match_info['direction_rel'].item()
    # new_obj.direction.data.extend([direction_dict['roll'], direction_dict['pitch'], direction_dict['yaw']])

    direction_dict = auto_label_match_info['direction_rel_vector'].item()
    new_obj.direction.data.extend([direction_dict['x'], direction_dict['y'], direction_dict['z']])

    print("save to: ", file_path)

    with open(file_path, 'w') as f:
        f.write(text_format.MessageToString(message))

def load_proto_file(file_path):
    """加载并解析prototxt文件"""
    with open(file_path, 'r') as f:
        proto_text = f.read()
    message = auto_labeling_label_pb2.LabelInfo()
    text_format.Parse(proto_text, message)
    
    return message


def extract_fields(proto_message):
    """从protobuf消息中提取所需字段"""
    transform_matrix = np.identity(4)

    for lidar_label in proto_message.auto_labeling_label.lidar_label:
        lidar2world_pose = lidar_label.lidar2world_pose
        objects = lidar_label.objects
        translation = {'x': np.nan, 'y': np.nan, 'z': np.nan}
        quaternion = {'qx': np.nan, 'qy': np.nan, 'qz': np.nan, 'qw': np.nan}
        translation['x'] = lidar2world_pose.translation.x 
        translation['y'] = lidar2world_pose.translation.y
        translation['z'] = lidar2world_pose.translation.z
        quaternion['qx'] = lidar2world_pose.quaternion.qx
        quaternion['qy'] = lidar2world_pose.quaternion.qy
        quaternion['qz'] = lidar2world_pose.quaternion.qz
        quaternion['qw'] = lidar2world_pose.quaternion.qw
        transform_matrix = pose_to_transform_matrix(translation, quaternion)
        label_proto = {}
        label_proto['timestamp_ns'] = lidar_label.timestamp_ns / 1e9
        label_proto['objs'] = []

        for obj in objects:
            if obj.type == "smallMot":
                data_attr = {}
                p = np.array([obj.center.data[0], obj.center.data[1], obj.center.data[2], 1])
                p_transformed = np.dot(transform_matrix, p)
                data_attr['center'] = p_transformed[:3]
                R_trans = transform_matrix[:3, :3]
                v_lidar = np.array([obj.velocity.data[0], obj.velocity.data[1], obj.velocity.data[2]])
                v_world = np.dot(R_trans, v_lidar)
                data_attr['velocity'] = v_world
                data_attr['velocity_lidar'] = v_lidar
                data_attr['timestamp_ns'] = lidar_label.timestamp_ns / 1e9
                data_attr['id'] = obj.id
                data_attr['track_id'] = obj.track_id
                label_proto['objs'].append(data_attr)
            
    return label_proto, transform_matrix

def extract_real_vel_results(json_filename):
    with open(json_filename, 'r') as file:
        json_data = json.load(file)
        return json_data
    
def binary_search_vel_json(ts, real_vel_json):
    left = 0
    right = len(real_vel_json) - 1
    prev, next_ = None, None
    while left <= right:
        mid = (left + right) // 2
        if ts == real_vel_json[mid][0]:
            return (mid, mid + 1 if mid + 1 < len(real_vel_json) else None)
        elif ts < real_vel_json[mid][0]:
            next_ = mid
            right = mid - 1
        else:
            prev = mid
            left = mid + 1
    
    # 处理边界情况
    if prev is None and next_ is not None:
        return (None, next_)
    elif next_ is None and prev is not None:
        return (prev, None)
    else:
        return (prev, next_)
        
def match_pos_with_gt(close_json, real_vel_json, label_proto_data):
    min_distance = 100000
    index = 0
    objs = label_proto_data['objs']
    for i in range(0, len(objs)):
        distance = np.linalg.norm(np.array(objs[i]['center']) - np.array(real_vel_json[close_json[0]][1]['position']))
        if distance < min_distance:
            min_distance = distance
            index = i
    return objs[index], min_distance



def match_auto_label_time(proto_message, label_proto_data, real_vel_json, transform_matrix):
    current_time = label_proto_data['timestamp_ns']
    close_json = binary_search_vel_json(current_time, real_vel_json)
    auto_label_match_info, min_distance = match_pos_with_gt(close_json, real_vel_json, label_proto_data)

    front_time = real_vel_json[close_json[0]][0]
    end_time = real_vel_json[close_json[1]][0]

    transform_matrix_inv = np.linalg.inv(transform_matrix[:3, :3])

    if (abs(current_time - front_time) > 0.015 and abs(current_time - end_time) > 0.015) or min_distance > 1.6:
        auto_label_match_info["velocity_rel"] = np.array([1000000000000.0, 1000000000000.0, 1000000000000.0])
        print("error time ")
    else:
        # 确定使用 front 或 end 数据，避免重复代码
        selected_index = close_json[0] if abs(end_time - current_time) > abs(front_time - current_time) else close_json[1]
        selected_data = real_vel_json[selected_index][1]

        auto_label_match_info["velocity_rel"] = np.dot(transform_matrix_inv, np.array(selected_data['velocity']))
        auto_label_match_info["acc"] = np.dot(transform_matrix_inv, np.array(selected_data['acc']))
        auto_label_match_info["angular_velocity"] = np.dot(transform_matrix_inv, np.array(selected_data['angular_velocity']))
        # auto_label_match_info["center"] = transform_world_to_lidar(np.array(selected_data['position']), transform_matrix)
        auto_label_match_info["turn_angle"] = np.array(selected_data['turn_angle'])
        auto_label_match_info["direction_rel_vector"] = np.array(selected_data['direction_rel_vector'])

        imu_center = transform_world_to_lidar(np.array(selected_data['position']), transform_matrix)
        imu_direction = np.array(selected_data['direction_rel_vector'])

        rear2imu_transform_matrix = extrinsics_to_transform_matrix(selected_data["vehicle_imu_extrinsics"])
        rear2body_transform_matrix = rear_to_body_transform(1.3555, 0, 0.533)
        imu2body_transformer_matrix = np.dot(rear2body_transform_matrix, np.linalg.inv(rear2imu_transform_matrix))

        target_center = calculate_target_center_in_self_car(imu_center, imu_direction, imu2body_transformer_matrix)
        auto_label_match_info["center"] = np.array(target_center)

        vx = auto_label_match_info["velocity_rel"][0]
        vy = auto_label_match_info["velocity_rel"][1]
        dx = auto_label_match_info['direction_rel_vector'].item()['x']
        dy = auto_label_match_info['direction_rel_vector'].item()['y']

        if vx == 0 and vy == 0:
            vel_dir_diff_angle = 0.0
        else:
            vel_angle = math.atan2(vy, vx)
            direction_angle = math.atan2(dy, dx)
            vel_dir_diff_angle = vel_angle - direction_angle

        auto_label_match_info["vel_dir_diff_angle"] = np.array(vel_dir_diff_angle)

    return auto_label_match_info

def process_directory(root_dir, real_vel_json):
    """遍历目录处理所有 auto_labeling.prototxt 文件，并与真值进行匹配"""
    error_file = "error.txt"  # 记录错误文件的路径
    results_rel = {}  # 存储相对速度结果
    results_lidar = {}  # 存储激光雷达速度结果
    result_acc = {}  # 存储加速度结果
    plt_save_path = ""  # 用于保存图表路径
    
    # 遍历目标目录
    for dirpath, dirnames, filenames in os.walk(root_dir):
        plt_save_path = dirpath  # 保存当前路径
        if 'auto_labeling.prototxt' in filenames:  # 查找目标文件
            file_path = os.path.join(dirpath, 'auto_labeling.prototxt')
            save_path = os.path.join(dirpath, 'auto_labeling_gt.prototxt')  # 保存修改后的文件路径
            
            try:
                # 1. 加载 Protobuf 文件
                proto_message = load_proto_file(file_path)
                
                # 2. 提取字段信息（包括目标物体信息和激光雷达位姿矩阵）
                label_proto_data, transform_matrix = extract_fields(proto_message)
                
                # 3. 匹配真值数据
                auto_label_match_info = match_auto_label_time(proto_message, label_proto_data, real_vel_json, transform_matrix)
                
                # 4. 检查匹配结果
                if auto_label_match_info["velocity_rel"][0] > 100000000.0:  # 检查是否匹配成功
                    # 若匹配失败，记录错误文件
                    if os.path.exists(error_file):
                        with open(error_file, 'a') as f:
                            f.write(f"处理文件 {file_path} 时未找到真值\n")
                    else:
                        with open(error_file, 'w') as f:
                            f.write(f"处理文件 {file_path} 时未找到真值\n")
                    print(f"处理文件 {file_path} 时未找到真值")
                    continue
                
                # 5. 存储匹配结果, for plot
                timestamp = label_proto_data['timestamp_ns']  # 获取时间戳
                results_rel[timestamp] = auto_label_match_info["velocity_rel"][:3]  # 相对速度
                results_lidar[timestamp] = auto_label_match_info["velocity_lidar"][:3]  # 激光雷达速度
                result_acc[timestamp] = auto_label_match_info["acc"][:3]  # 加速度
                
                # 6. 保存新的 Protobuf 文件
                save_proto_to_text_file(auto_label_match_info, proto_message, save_path, label_proto_data, transform_matrix)
                
                # 7. 打印上传路径
                upload_path = dirpath.split("rt6_wuhan_cutin_flow_prototxt/")[1] + "/"  # 提取路径
                path_all = Path(upload_path)
                parts = list(path_all.parts)
                parts[1] = "high_level_" + parts[1]
                new_path = Path(*parts)
                cmd = f'/usr/local/bin/bcecmd bos cp -ry {save_path} bos:/l4-adfm-rt6/auto-labeling/label/{str(new_path)}/'
                print(cmd)
                # os.system(cmd)  # 上传命令（根据实际需求决定是否运行）
            
            except Exception as e:
                # 捕获异常并记录错误
                print(f"处理文件 {file_path} 时出错: {str(e)}")
                if os.path.exists(error_file):
                    with open(error_file, 'a') as f:
                        f.write(f"处理文件 {file_path} 时发生错误: {str(e)}\n")
                else:
                    with open(error_file, 'w') as f:
                        f.write(f"处理文件 {file_path} 时发生错误: {str(e)}\n")
    
    # # 可选：绘制对比图表
    # generate_velocity_comparison_chart(results_rel, results_lidar, result_acc, plt_save_path)


def generate_velocity_comparison_chart(results_rel, results_lidar, result_acc, save_path):
    """生成速度和加速度对比的可视化图表"""
    if not results_rel or not results_lidar or not result_acc:
        print("没有足够的数据生成图表")
        return

    # 数据准备
    sorted_times = sorted(results_rel.keys())
    time_labels = [str(ts) for ts in sorted_times]  # 时间戳转为字符串标签
    vel_res = [results_rel[ts] for ts in sorted_times]
    vel_lidar = [results_lidar[ts] for ts in sorted_times]
    vel_acc = [result_acc[ts] for ts in sorted_times]
    
    # 创建图表
    plt.figure(figsize=(12, 6))  # 加宽画布
    plt.subplot(1, 3, 1)
    plt.plot(time_labels, [v[0] for v in vel_res], label='Relative Velocity (X)', linestyle='-', linewidth=1, color='#1f77b4')
    plt.plot(time_labels, [v[0] for v in vel_lidar], label='Lidar Velocity (X)', linestyle='--', linewidth=1, color='#ff7f0e')
    plt.title('X-Axis Velocity Comparison')
    plt.xlabel('Timestamp')
    plt.ylabel('Velocity (m/s)')
    plt.xticks(rotation=45, ha='right')
    plt.grid(True, linestyle='--', alpha=0.6)
    plt.legend(frameon=True, loc='best')

    plt.subplot(1, 3, 2)
    plt.plot(time_labels, [v[1] for v in vel_res], label='Relative Velocity (Y)', linestyle='-', linewidth=1, color='#1f77b4')
    plt.plot(time_labels, [v[1] for v in vel_lidar], label='Lidar Velocity (Y)', linestyle='--', linewidth=1, color='#2ca02c')
    plt.title('Y-Axis Velocity Comparison')
    plt.xlabel('Timestamp')
    plt.ylabel('Velocity (m/s)')
    plt.xticks(rotation=45, ha='right')
    plt.grid(True, linestyle='--', alpha=0.6)
    plt.legend(frameon=True, loc='best')

    plt.subplot(1, 3, 3)
    plt.plot(time_labels, [v[0] for v in vel_acc], label='Acceleration (X)', linestyle='-', linewidth=1, color='#1f77b4')
    plt.plot(time_labels, [v[1] for v in vel_acc], label='Acceleration (Y)', linestyle='--', linewidth=1, color='#2ca02c')
    plt.plot(time_labels, [v[2] for v in vel_acc], label='Acceleration (Z)', linestyle=':', linewidth=1, color='#ff7f0e')
    plt.title('Acceleration Comparison')
    plt.xlabel('Timestamp')
    plt.ylabel('Acceleration (m/s²)')
    plt.xticks(rotation=45, ha='right')
    plt.grid(True, linestyle='--', alpha=0.6)
    plt.legend(frameon=True, loc='best')

    plt.tight_layout()
    file_name = os.path.join(save_path, 'velocity_comparison.png')
    plt.savefig(file_name, dpi=300, bbox_inches='tight')
    print(f"图表已保存至: {file_name}")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='处理auto_labeling.prototxt文件')
    parser.add_argument('directory', help='auto_label_proto_vel文件夹路径')
    parser.add_argument('json_folder', help='localization_gt_json文件夹路径')
    args = parser.parse_args()
    
    # 检查目录是否存在
    if not os.path.isdir(args.directory):
        print(f"错误: 目录 {args.directory} 不存在")
        return  
    if not os.path.isdir(args.json_folder):
        print(f"错误: 目录 {args.json_folder} 不存在")
        return  
    
    # 遍历auto_label_proto_vel目录
    for data_package in os.listdir(args.directory):
        package_path = os.path.join(args.directory, data_package)
        if not os.path.isdir(package_path):
            continue
        
        # 构造第一个JSON文件路径
        json_file_ego = os.path.join(args.json_folder, f"{data_package}_localization.json")
        if not os.path.exists(json_file_ego):
            print(f"未找到对应的ego JSON文件: {json_file_ego}")
            continue
        
        # 构造第二个JSON文件路径
        target_json_files = [
            fname for fname in os.listdir(args.json_folder)
            if "JME1567" in fname and fname.endswith("_localization.json")
        ]
        
        if not target_json_files:
            print(f"未找到包含JME1567的JSON文件: {data_package}")
            continue

        json_file_target = None
        max_overlap = 0
        
        parts = data_package.split("_")
        try:
            ego_start_time = int(parts[-2])
            ego_end_time   = int(parts[-1])
        except (IndexError, ValueError):
            print("无法解析自车时间戳")

        for target_file in target_json_files:
            target_file_wo_suffix = target_file.replace("_localization.json", "")
            parts_target = target_file_wo_suffix.split("_")
            try:
                target_start_time = int(parts_target[-2])
                target_end_time   = int(parts_target[-1])
            except (IndexError, ValueError):
                print("无法解析目标车时间戳")

            # 计算时间范围重叠度
            overlap_start = max(ego_start_time, target_start_time)
            overlap_end = min(ego_end_time, target_end_time)
            overlap = max(0, overlap_end - overlap_start)

            if overlap > max_overlap:
                max_overlap = overlap
                json_file_target = os.path.join(args.json_folder, target_file)
        
        if not json_file_target:
            print(f"未找到与时间范围匹配的JME1567 JSON文件: {data_package}")
            continue

        print(f"处理数据包: {data_package}")
        print(f"使用ego JSON: {json_file_ego}")
        print(f"使用target JSON: {json_file_target}")
        
        # 读取和处理数据
        real_vel_ego_json = extract_real_vel_results(json_file_ego)
        real_vel_target_json = extract_real_vel_results(json_file_target)
        real_vel_match_json = match_and_calculate_direction_vector(real_vel_ego_json, real_vel_target_json, time_threshold=0.015)
        process_directory(package_path, real_vel_match_json)

if __name__ == '__main__':
    main()