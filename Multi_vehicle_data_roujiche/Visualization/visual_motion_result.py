import os
import numpy as np
import cv2
from google.protobuf import text_format
import auto_labeling_label_pb2
from pathlib import Path

def load_proto_file(file_path):
    """加载并解析 auto_labeling_gt.prototxt 文件"""
    with open(file_path, 'r') as f:
        proto_text = f.read()
    message = auto_labeling_label_pb2.LabelInfo()
    text_format.Parse(proto_text, message)
    return message

def calculate_velocity_difference(proto_message):
    """
    计算速度绝对值之差
    提取 label_src 为 gt_vel 的目标，并找到具有相同 track_id 的目标。
    """
    velocity_differences = []
    for lidar_label in proto_message.auto_labeling_label.lidar_label:
        objects = lidar_label.objects
        gt_vel_objects = [
            obj for obj in objects if obj.label_src == "gt_vel"
        ]
        
        for gt_obj in gt_vel_objects:
            gt_track_id = gt_obj.track_id
            gt_velocity = np.array(gt_obj.velocity.data)

            # 找到同一 track_id 的其他目标
            matched_objects = [
                obj for obj in objects if obj.track_id == gt_track_id and obj != gt_obj
            ]
            for matched_obj in matched_objects:
                matched_velocity = np.array(matched_obj.velocity.data)
                velocity_diff = np.linalg.norm(gt_velocity - matched_velocity)
                velocity_differences.append(velocity_diff)
    
    return velocity_differences

def process_directory(root_dir):
    """
    遍历帧文件夹，读取 auto_labeling_gt.prototxt 文件，计算速度差，并按时间顺序绘图。
    """
    frame_dirs = sorted(
        [os.path.join(root_dir, d) for d in os.listdir(root_dir) if os.path.isdir(os.path.join(root_dir, d))],
        key=lambda x: int(Path(x).name)  # 按时间戳排序
    )

    velocity_differences_by_time = []

    for frame_dir in frame_dirs:
        for sub_dir, _, files in os.walk(frame_dir):
            if 'auto_labeling_gt.prototxt' in files:
                proto_file = os.path.join(sub_dir, 'auto_labeling_gt.prototxt')
                proto_message = load_proto_file(proto_file)

                # 计算速度差
                velocity_differences = calculate_velocity_difference(proto_message)
                if velocity_differences:
                    timestamp = int(Path(frame_dir).name)  # 以帧时间戳为横轴
                    avg_velocity_diff = np.mean(velocity_differences)  # 计算平均速度差
                    velocity_differences_by_time.append((timestamp, avg_velocity_diff))
                break

    # 按时间戳排序
    velocity_differences_by_time.sort(key=lambda x: x[0])

    # 绘图
    if velocity_differences_by_time:
        timestamps, velocity_differences = zip(*velocity_differences_by_time)
        plot_velocity_differences(timestamps, velocity_differences)

def plot_velocity_differences(timestamps, velocity_differences):
    """使用 OpenCV 绘制速度差随时间变化图"""
    max_velocity_diff = max(velocity_differences)
    min_velocity_diff = min(velocity_differences)
    img_height = 600
    img_width = 1200
    margin = 50

    # 创建画布
    img = np.ones((img_height, img_width, 3), dtype=np.uint8) * 255

    # 绘制坐标轴
    cv2.line(img, (margin, img_height - margin), (img_width - margin, img_height - margin), (0, 0, 0), 2)
    cv2.line(img, (margin, margin), (margin, img_height - margin), (0, 0, 0), 2)

    # 归一化速度差
    normalized_diffs = [
        int((diff - min_velocity_diff) / (max_velocity_diff - min_velocity_diff) * (img_height - 2 * margin))
        for diff in velocity_differences
    ]

    # 绘制数据点
    for i, (timestamp, norm_diff) in enumerate(zip(timestamps, normalized_diffs)):
        x = margin + int(i * (img_width - 2 * margin) / len(timestamps))
        y = img_height - margin - norm_diff
        cv2.circle(img, (x, y), 3, (0, 0, 255), -1)

        # 绘制时间戳标签
        if i % max(1, len(timestamps) // 10) == 0:  # 控制标签密度
            cv2.putText(img, str(timestamp), (x - 20, img_height - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 0), 1)

    # 绘制标题
    cv2.putText(img, "Velocity Difference Over Time", (margin, margin // 2), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)

    # 显示图像
    cv2.imshow("Velocity Differences", img)
    cv2.waitKey(0)
    cv2.destroyAllWindows()

if __name__ == "__main__":
    root_dir = "/home/<USER>/local_projects/baidu/personal-code/BAIDU_TOOLS_QSZ/Multi_vehicle_data_roujiche/rt6_wuhan_motion_switch_prototxt/multi-adfm-line-1748615158-1_JME1562_20250527100000_1748313356_1748313376/adfm_500.6.0.1_motion/20250527/JME1562/JME1562_20250527100000/JME1562_20250527100000_1748313356_1748313376"
    process_directory(root_dir)