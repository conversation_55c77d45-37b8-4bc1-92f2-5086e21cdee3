import os
import subprocess

# 1. 配置
data_file       = "./Multi_vehicle_data_roujiche/multi_velhicle_bag_name/ego_bag_出遮挡.txt"
target_folder   = "adfm_500.6.0.1_motion/"            # 保持结尾 /，只拷贝里面的内容
local_save_root = "./Multi_vehicle_data_roujiche/rt6_wuhan_occluded_prototxt"

# 2. 确保本地根目录存在
os.makedirs(local_save_root, exist_ok=True)

# 3. 读取数据包列表
with open(data_file, encoding="utf-8") as f:
    data_packages = [line.strip() for line in f if line.strip()]

# 4. 逐包下载
for package in data_packages:
    # 远端 BOS 路径： …/package/adfm_500.6.0.1_motion/
    bos_path = f"bos://l4-adfm-rt6/auto-labeling/label/{package}/{target_folder}"

    # 本地保存路径： …/package/adfm_500.6.0.1_motion/
    package_save_path = os.path.join(local_save_root, package, target_folder.rstrip("/"))
    os.makedirs(package_save_path, exist_ok=True)

    # bcecmd 命令
    cmd = ["bcecmd", "bos", "cp", "-r", bos_path, package_save_path]
    print("Executing:", " ".join(cmd))
    subprocess.run(cmd, check=True)

print("所有数据包的目标版本文件夹已下载完成！")