import numpy as np

def extrinsics_to_transform_matrix(vehicle_imu_extrinsics):
    """
    将从后轮中心到 IMU 坐标系的旋转和平移信息转换为 4x4 齐次变换矩阵。

    参数:
        vehicle_imu_extrinsics (dict): 包含旋转（四元数）和平移的转换关系。
            - rotation: 四元数表示的旋转 [qx, qy, qz, qw]
            - translation: 平移向量 [tx, ty, tz]

    返回:
        np.array: 4x4 齐次变换矩阵
    """
    # 提取旋转 (四元数) 和平移
    qx, qy, qz, qw = vehicle_imu_extrinsics["rotation"]
    tx, ty, tz = vehicle_imu_extrinsics["translation"]

    # 归一化四元数
    norm = np.sqrt(qx**2 + qy**2 + qz**2 + qw**2)
    qx, qy, qz, qw = qx / norm, qy / norm, qz / norm, qw / norm

    # 将四元数转换为 3x3 旋转矩阵
    rotation_matrix = np.array([
        [1 - 2 * (qy**2 + qz**2), 2 * (qx * qy - qz * qw), 2 * (qx * qz + qy * qw)],
        [2 * (qx * qy + qz * qw), 1 - 2 * (qx**2 + qz**2), 2 * (qy * qz - qx * qw)],
        [2 * (qx * qz - qy * qw), 2 * (qy * qz + qx * qw), 1 - 2 * (qx**2 + qy**2)]
    ])

    # 构造 4x4 的齐次变换矩阵
    transform_matrix = np.identity(4)  # 初始化为单位矩阵
    transform_matrix[:3, :3] = rotation_matrix  # 填入旋转矩阵
    transform_matrix[:3, 3] = [tx, ty, tz]  # 填入平移向量

    return transform_matrix

def rear_to_body_transform(dx, dy, dz):
    """
    生成从 target 车的后轮中心到车体中心的 4x4 齐次变换矩阵。

    返回:
        np.array: 4x4 齐次变换矩阵
    """
    # 初始化 4x4 单位矩阵
    transform_matrix = np.identity(4)

    # 平移部分：后轮中心到车体中心的偏移 [x, y, z]
    transform_matrix[0, 3] = -dx  # x 方向平移
    transform_matrix[1, 3] = -dy       # y 方向平移
    transform_matrix[2, 3] = -dz   # z 方向平移

    return transform_matrix

# def calculate_target_center_in_self_car(imu_center, imu_direction, rear2imu_transform_matrix, rear2body_transform_matrix):
#     """
#     计算检测到的目标车辆中心在自车坐标系中的位置。

#     参数:
#         imu_center (np.array): 检测到的车辆 IMU 在自车坐标系中的位置 [x, y, z]
#         imu_direction (np.array): 检测到的车辆方向矢量（在自车坐标系中）。
#         rear2imu_transform_matrix (np.array): 后轮中心到 IMU 的 4x4 转换矩阵。
#         rear2body_transform_matrix (np.array): 后轮中心到车辆中心的 4x4 转换矩阵。

#     返回:
#         np.array: 目标车辆中心在自车坐标系中的位置 [x, y, z]
#     """
#     # 将 IMU 中心位置扩展为齐次坐标 [x, y, z, 1]
#     imu_center_homogeneous = np.append(imu_center, 1)

#     # 计算从 IMU 到后轮中心的逆变换矩阵（将 IMU 中心位置映射到后轮中心坐标系）
#     imu2rear_transform_matrix = np.linalg.inv(rear2imu_transform_matrix)

#     # 将 IMU 坐标转换到后轮中心坐标系
#     rear_center_homogeneous = np.dot(imu2rear_transform_matrix, imu_center_homogeneous)

#     # 将后轮中心坐标转换到车辆中心坐标系
#     body_center_homogeneous = np.dot(rear2body_transform_matrix, rear_center_homogeneous)

#     # 返回车辆中心在自车坐标系中的位置（去掉齐次坐标的最后一位）
#     return body_center_homogeneous[:3]

def get_imu_to_ego_transform_matrix(imu_center, imu_direction):
    """
    根据目标车辆 IMU 中心位置和方向向量，构造 IMU 到 ego 坐标系的齐次变换矩阵。
    """
    # Step 1: 构造旋转矩阵
    forward = imu_direction / np.linalg.norm(imu_direction)  # 单位化方向向量
    up = np.array([0, 0, 1])  # 假设 z 轴为上方向
    right = np.cross(up, forward)  # 右方向
    right = right / np.linalg.norm(right)  # 单位化右方向
    up = np.cross(forward, right)  # 更新上方向，确保正交性

    # 构造旋转矩阵 (3x3)
    rotation_matrix = np.vstack([right, up, forward]).T

    # Step 2: 构造齐次变换矩阵 (4x4)
    imu_to_ego_transform_matrix = np.eye(4)
    imu_to_ego_transform_matrix[:3, :3] = rotation_matrix  # 旋转部分
    imu_to_ego_transform_matrix[:3, 3] = imu_center  # 平移部分

    return imu_to_ego_transform_matrix