import numpy as np

def extrinsics_to_transform_matrix(vehicle_imu_extrinsics):
    """
    将从后轮中心到 IMU 坐标系的旋转和平移信息转换为 4x4 齐次变换矩阵。

    参数:
        vehicle_imu_extrinsics (dict): 包含旋转（四元数）和平移的转换关系。
            - rotation: 四元数表示的旋转 [qx, qy, qz, qw]
            - translation: 平移向量 [tx, ty, tz]

    返回:
        np.array: 4x4 齐次变换矩阵
    """
    # 提取旋转 (四元数) 和平移
    qx, qy, qz, qw = vehicle_imu_extrinsics["rotation"]
    tx, ty, tz = vehicle_imu_extrinsics["translation"]

    # 归一化四元数
    norm = np.sqrt(qx**2 + qy**2 + qz**2 + qw**2)
    qx, qy, qz, qw = qx / norm, qy / norm, qz / norm, qw / norm

    # 将四元数转换为 3x3 旋转矩阵
    rotation_matrix = np.array([
        [1 - 2 * (qy**2 + qz**2), 2 * (qx * qy - qz * qw), 2 * (qx * qz + qy * qw)],
        [2 * (qx * qy + qz * qw), 1 - 2 * (qx**2 + qz**2), 2 * (qy * qz - qx * qw)],
        [2 * (qx * qz - qy * qw), 2 * (qy * qz + qx * qw), 1 - 2 * (qx**2 + qy**2)]
    ])

    # 构造 4x4 的齐次变换矩阵
    transform_matrix = np.identity(4)  # 初始化为单位矩阵
    transform_matrix[:3, :3] = rotation_matrix  # 填入旋转矩阵
    transform_matrix[:3, 3] = [tx, ty, tz]  # 填入平移向量

    return transform_matrix

def rear_to_body_transform(dx, dy, dz):
    """
    生成从 target 车的后轮中心到车体中心的 4x4 齐次变换矩阵。

    返回:
        np.array: 4x4 齐次变换矩阵
    """
    # 初始化 4x4 单位矩阵
    transform_matrix = np.identity(4)

    # 平移部分：后轮中心到车体中心的偏移 [x, y, z]
    transform_matrix[0, 3] = -dx  # x 方向平移
    transform_matrix[1, 3] = -dy       # y 方向平移
    transform_matrix[2, 3] = -dz   # z 方向平移

    return transform_matrix


def calculate_target_car_center_in_self_car(imu_direction, imu_center, body2imu_transform_matrix):
    """给定本地 +X 轴在世界坐标中的方向，生成一个右手旋转矩阵"""
    direction_np = [imu_direction.item()["x"], imu_direction.item()["y"], imu_direction.item()["z"]]
    x_axis = direction_np / np.linalg.norm(direction_np)

    world_up = np.array([0., 0., 1.])
    # 若 x 与 up 共线，用另一个 up 向量避免退化
    if abs(np.dot(x_axis, world_up)) > 0.999:
        world_up = np.array([0., 1., 0.])

    y_axis = np.cross(world_up, x_axis)
    y_axis /= np.linalg.norm(y_axis)

    z_axis = np.cross(x_axis, y_axis)

    R_imu2lidar = np.column_stack((x_axis, y_axis, z_axis))

    # 组装 4×4 的齐次矩阵 T_imu2lidar
    T_imu2lidar = np.eye(4)
    T_imu2lidar[:3, :3] = R_imu2lidar
    T_imu2lidar[:3, 3]  = imu_center

    # lidar ← imu ← body ，连乘得到 lidar ← body
    T_body2lidar = T_imu2lidar @ body2imu_transform_matrix

    # 目标 C（body 坐标系原点）在 lidar 中的 3D 位置
    C_pos_lidar = T_body2lidar[:3, 3]

    return C_pos_lidar, T_imu2lidar

def calculate_target_velocity(
        v_imu_world,           # IMU 线速度（world frame, shape (3,)）
        yaw_rate_rear,         # 后轴坐标系下的角速度 ω_z  (scalar)
        T_w2lidar,               # world → lidar 4×4
        imu_center_lidar,        # IMU 在 lidar 中的位置  (3,)
        body_center_lidar,       # 质心在 lidar 中的位置  (3,)
        T_imu2lidar,             # imu → lidar 4×4
        T_rear2imu):           # rear → imu 4×4
    """返回目标车质心在 lidar frame 下的速度 (3,)"""

    # ---------- 1. v_imu_world → v_imu_lidar ----------
    R_w2lidar     = T_w2lidar[:3, :3]        # 只要旋转
    v_imu_lidar   = R_w2lidar @ v_imu_world  # (3,)

    # ---------- 2. 坐标系间旋转矩阵 ----------
    R_imu2lidar   = T_imu2lidar[:3, :3]
    R_lidar2imu   = R_imu2lidar.T
    R_rear2imu  = T_rear2imu[:3, :3]
    R_imu2rear  = R_rear2imu.T

    R_lidar2rear  = R_imu2rear @ R_lidar2imu   # lidar → rear
    R_rear2lidar  = R_lidar2rear.T            # rear → lidar

    # ---------- 3. 把 r 向量旋到 rear ----------
    r_lidar   = body_center_lidar - imu_center_lidar   # (3,)
    r_rear  = R_lidar2rear @ r_lidar                 # (3,)

    # ---------- 4. 角速度（已在 rear） ----------
    omega_rear = np.array([0., 0., float(yaw_rate_rear)])

    # ---------- 5. 质心线速度 ----------
    v_imu_rear     = R_lidar2rear @ v_imu_lidar
    v_rot_rear     = np.cross(omega_rear, r_rear)
    v_center_rear  = v_imu_rear + v_rot_rear
    v_center_lidar   = R_rear2lidar @ v_center_rear

    return v_center_lidar


# def calculate_target_velocity(v_imu_lidar, omega_lidar, imu_center, target_center, T_imu2lidar, rear2imu_transform_matrix):
#     imu_center_lidar = imu_center          # IMU 在 lidar 中的位置  (3,)
#     body_center_lidar = target_center      # 车辆质心在 lidar 中的位置 (3,)

#     # 已经构造好的变换矩阵
#     rear2imu_matrix = rear2imu_transform_matrix   # ← extrinsics_to_transform_matrix 返回

#     # ========= 计算所有用得到的旋转矩阵 =========
#     # 只要旋转，不要平移
#     R_imu2lidar  = T_imu2lidar[:3, :3]                    # IMU→lidar
#     R_lidar2imu  = R_imu2lidar.T                         # lidar→IMU
#     R_rear2imu = rear2imu_matrix[:3, :3]             # Rear→IMU
#     R_imu2rear = R_rear2imu.T                        # IMU→Rear

#     # lidar→Rear 的总旋转： lidar→IMU→Rear
#     R_lidar2rear = R_imu2rear @ R_lidar2imu
#     # 反向 Rear→lidar
#     R_rear2lidar = R_lidar2rear.T

#     # ========= 把速度、角速度旋到 Rear frame =========
#     v_imu_rear = R_lidar2rear @ v_imu_lidar

#     # 角速度可能只有 z 分量；先补成 3 维再旋转
#     if omega_lidar.size == 1:
#         omega_lidar = np.array([0., 0., float(omega_lidar)])
#     omega_rear = R_lidar2rear @ omega_lidar

#     # ========= 计算 r 向量并补偿转动速度 =========
#     r_lidar   = body_center_lidar - imu_center_lidar       # IMU → BodyCenter 位移 (lidar)
#     r_rear  = R_lidar2rear @ r_lidar                     # 同一向量在 Rear 中的分量

#     v_rot_rear   = np.cross(omega_rear, r_rear)      # ω × r
#     v_center_rear = v_imu_rear + v_rot_rear          # 质心线速度（Rear 表达）

#     # ========= 旋回 lidar 并存结果 =========
#     v_center_lidar = R_rear2lidar @ v_center_rear

#     return v_center_lidar