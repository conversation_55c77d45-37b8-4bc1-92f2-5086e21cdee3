# 忽略所有 .log 文件
*.log

# 忽略所有临时文件
*.tmp

# 忽略某个文件夹
/bicycle_ctra_model/synthetic_autonomous_driving_data/
/gen_auto_labeling_result/meta/
/gen_auto_labeling_result/roujiche/
/Multi_vehicle_data_roujiche/auto_label_proto_vel/
/Multi_vehicle_data_roujiche/localization_gt_json/
/Multi_vehicle_data_roujiche/rt6_wuhan_acc_dec_flow_prototxt/
/Multi_vehicle_data_roujiche/rt6_wuhan_cutin_flow_prototxt/
/Multi_vehicle_data_roujiche/rt6_wuhan_occluded_prototxt/
/Multi_vehicle_data_roujiche/rt6_wuhan_motion_switch_prototxt/
/Multi_vehicle_data_roujiche/bk

# 忽略所有 .DS_Store 文件（macOS 系统）
.DS_Store

# 忽略 node_modules 文件夹（Node.js 项目）
node_modules/

# 忽略 Python 的虚拟环境
venv/

# 忽略编译输出文件
build/
dist/

# 忽略特定文件
secrets.json
