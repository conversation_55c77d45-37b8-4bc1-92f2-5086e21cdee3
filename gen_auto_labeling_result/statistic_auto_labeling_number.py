import os

def count_files_with_name(root_dir, target_name):
    """
    统计指定目录及其子目录中指定文件名的文件数量

    Args:
    - root_dir (str): 根目录路径
    - target_name (str): 目标文件名

    Returns:
    - int: 文件总数量
    """
    count = 0
    for dirpath, _, filenames in os.walk(root_dir):
        count += filenames.count(target_name)
    return count

if __name__ == "__main__":
    # 指定根目录
    root_dir = "/home/<USER>/local_projects/baidu/personal-code/BAIDU_TOOLS_QSZ/Multi_vehicle_data_roujiche/rt6_wuhan_acc_dec_flow_prototxt"
    # 目标文件名
    target_name = "auto_labeling.prototxt"

    # 统计数量
    total_count = count_files_with_name(root_dir, target_name)
    print(f"Total number of '{target_name}' files: {total_count}")