#!/bin/bash
# 用途：把源目录下的所有子文件夹软链接到目标目录
# 使用：bash link_dirs.sh

# 源目录（修改这里）
SRC="/home/<USER>/local_projects/baidu/personal-code/BAIDU_TOOLS_QSZ/Multi_vehicle_data_roujiche/rt6_wuhan_acc_dec_flow_prototxt/"

# 目标目录（修改这里）
DST="/home/<USER>/local_projects/baidu/personal-code/BAIDU_TOOLS_QSZ/gen_auto_labeling_result/roujiche/rt6_wuhan_acc_dec/prod_line/label/"

# 确保目标目录存在
mkdir -p "$DST"

# 遍历源目录下的子文件夹
for d in "$SRC"/*/; do
    folder_name=$(basename "$d")
    target_link="$DST/$folder_name"

    # 如果目标已存在，删除后再创建软链接
    if [ -e "$target_link" ]; then
        echo "⚠️ 已存在: $target_link ，删除后重建链接"
        rm -rf "$target_link"
    fi

    ln -s "$d" "$target_link"
    echo "✅ 已链接: $target_link -> $d"
done

echo "🎉 全部完成！"
