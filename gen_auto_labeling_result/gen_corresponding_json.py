# it is used to match with gt
import os
import json
from collections import defaultdict
from easydict import EasyDict as edict

def match(task_info_p, meta_dir, root_dir, output_name, list_name="input_list", bucket_name="l4-adfm-rt6"):
    task_info = set()
    with open(task_info_p, 'r') as f:
        lines = f.readlines()
    for line in lines:
        task_info.add(line.strip())
    task_info = list(task_info)
    # download task info
    total_list = defaultdict(list)
    for task in task_info:
        bos_taskinfo_p = f"bos:/{bucket_name}/auto-labeling/task_info/{task}.json"
        local_taskinfo_p = os.path.join(root_dir, f"{task}.json")
        cmd = f"bcecmd bos cp -ry {bos_taskinfo_p} {local_taskinfo_p}"
        print(cmd)
        os.system(cmd)
        if os.path.exists(local_taskinfo_p):
            with open(local_taskinfo_p, 'r') as f:
                data = json.load(f)
                input_data = data[list_name]
                total_list[task] = input_data
                
    json_files = os.listdir(meta_dir)
    json_files = sorted(json_files, key=lambda x: int(x.split(".")[0]))
    file_list = []
    for file in json_files:
        if not file.endswith('.json'):
            continue
        file_p = os.path.join(meta_dir, file)
        with open(file_p, 'r') as f:
            data = json.load(f)
        file_name = data['filename']
        one_data = edict()
        one_data.json_name = file
        one_data.file_name = file_name
        file_list.append(one_data)
        
    json_map = defaultdict(dict)
    for data in file_list:
        file_name = data.file_name
        json_name = data.json_name
        task_subname = file_name.split("/")[-3]
        file_ts = int(file_name.split("/")[-2])
        for task in total_list:
            if task_subname in task:
                task_list = total_list[task]
                for ll in task_list:
                    ts = int(ll.split("/")[-1])
                    if ts != file_ts:
                        continue
                    # if ts in total_list[task]:
                    #     json_map[json_name]["task_name"] = task
                    #     json_map[json_name]["file_name"] = ll
                    json_map[json_name]["task_name"] = task
                    json_map[json_name]["file_name"] = ll
                    json_map[json_name]["model_file_name"] = file_name
                    json_map[json_name]["bucket_name"] = bucket_name
    
    dump_json_p = os.path.join(root_dir, f"{output_name}.json")
    with open(dump_json_p, 'w') as f:
        json.dump(json_map, f, indent=4)
        
        
if __name__ == "__main__":
    task_info_p = "gen_auto_labeling_result/roujiche/task_list.txt" # 整体的list
    meta_dir = "gen_auto_labeling_result/meta/meta_acc_dec/meta" # 模型的meta信息json集
    root_dir = "/home/<USER>/local_projects/baidu/personal-code/BAIDU_TOOLS_QSZ/gen_auto_labeling_result/roujiche/rt6_wuhan_acc_dec/assign" # 存放路径，生成一个对应的json，模型的帧数和自标prototxt的对应关系
    match(task_info_p, meta_dir, root_dir, "rt6_wuhan_acc_dec")