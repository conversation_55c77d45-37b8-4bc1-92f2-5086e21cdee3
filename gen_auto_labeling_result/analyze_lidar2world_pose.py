import os
import json
import numpy as np
import tqdm
from multiprocessing import Pool
from google.protobuf import text_format
import auto_labeling_label_pb2

# 定义类型映射
type_map = {
    "TrainedOthers": "UNMOVABLE",
    "smallMot": "CAR",
    "bigMot": "TRUCK",
    "nonMot": "CYCLIST",
    "pedestrian": "PEDESTRIAN",
    "accessory_main": "MAIN",
    "accessory_body": "BODY"
}

def download_and_process_file(args):
    """
    下载并处理单个文件
    :param args: 包含 JSON 数据、根目录、版本信息、JSON 键名
    :return: 处理结果
    """
    json_name, json_data, root_dir, version = args
    
    # 从 JSON 数据中提取信息
    bucket_name = json_data["bucket_name"]
    relative_path = json_data["file_name"]
    task_name = json_data["task_name"]
    
    # 定义文件路径
    bos_path = f"bos:/{bucket_name}/auto-labeling/label/{task_name}/{version}/{relative_path}/labels/multi_task_label.prototxt"
    local_path = f"{root_dir}/label/{task_name}/{version}/{relative_path}/labels/multi_task_label.prototxt"
    
    # 下载 multi_task_label.prototxt 文件
    if not os.path.exists(local_path):
        cmd = f"bcecmd bos cp -ry {bos_path} {local_path}"
        print(f"Downloading {bos_path} to {local_path}")
        os.system(cmd)
    else:
        print(f"{local_path} already exists. Skipping download.")
    
    # 检查文件是否下载成功
    if not os.path.exists(local_path):
        return f"Error: File {local_path} not found after download."
    
    # 解析 lidar2world_pose 信息
    try:
        with open(local_path, 'rb') as f:
            lines = f.read()
        label_info = auto_labeling_label_pb2.LabelInfo()
        label_info.ParseFromString(lines)
        
        # 提取 lidar2world_pose 信息
        if label_info.HasField("auto_labeling_label"):
            auto_labeling_label = label_info.auto_labeling_label
            if auto_labeling_label.lidar_label:
                lidar = auto_labeling_label.lidar_label[0].lidar2world_pose
                lidar_info = [
                    round(lidar.translation.x, 2),
                    round(lidar.translation.y, 2),
                    round(lidar.translation.z, 2),
                    round(lidar.quaternion.qx, 6),
                    round(lidar.quaternion.qy, 6),
                    round(lidar.quaternion.qz, 6),
                    round(lidar.quaternion.qw, 6)
                ]
            else:
                return f"No lidar_label found in {local_path}"
        else:
            return f"No auto_labeling_label found in {local_path}"
    except Exception as e:
        return f"Error parsing {local_path}: {e}"
    
    # 保存解析结果并重命名文件
    base_name = os.path.splitext(json_name)[0]  # 去掉 .json 后缀
    output_dir = os.path.join(root_dir, "lidar2world_pose")
    os.makedirs(output_dir, exist_ok=True)
    output_file = os.path.join(output_dir, f"{base_name}.txt")
    with open(output_file, 'w') as f:
        f.write(" ".join(map(str, lidar_info)))
    
    return f"Successfully processed and saved to {output_file}"


def main():
    # 配置路径和版本信息
    json_file_path = "gen_auto_labeling_result/roujiche/rt6_wuhan_acc_dec/assign/rt6_wuhan_acc_dec.json"
    root_dir = "/home/<USER>/local_projects/baidu/personal-code/BAIDU_TOOLS_QSZ/gen_auto_labeling_result/roujiche/rt6_wuhan_acc_dec/prod_line"
    version = "adfm_500.6.0.1_motion"
    
    # 加载 JSON 文件
    try:
        with open(json_file_path, 'r') as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"Error: JSON file {json_file_path} not found.")
        return
    except json.JSONDecodeError as e:
        print(f"Error: Failed to parse JSON file - {e}")
        return
    
    # 准备多进程任务
    args_list = [(json_name, json_data, root_dir, version) for json_name, json_data in data.items()]
    
    # 使用多进程下载和处理文件
    with Pool(processes=16) as pool:
        results = list(tqdm.tqdm(pool.imap(download_and_process_file, args_list), total=len(args_list), desc="Processing files"))
    
    # 输出结果
    for result in results:
        print(result)


if __name__ == "__main__":
    main()