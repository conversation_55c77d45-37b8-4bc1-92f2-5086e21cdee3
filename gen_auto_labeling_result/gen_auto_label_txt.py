import os
import json
import numpy as np
import sys
sys.path.append(os.getcwd())
from multiprocessing import Pool
import tqdm
import auto_labeling_label_pb2 
from google.protobuf import text_format
import math


type_map = {"TrainedOthers": "UNMOVABLE", 
            "smallMot":"CAR",
            "bigMot":"TRUCK",
            "nonMot":"CYCLIST",
            "pedestrian":"PEDESTRIAN",
            "accessory_main":"MAIN",
            "accessory_body": "BODY"}
error_code = {"-1": "not exist", "0": "success"}

def process_one_frame(root_dir, version, json_name, json_data):
    numbering_str = json_name.split(".")[0]
    numbering_int = int(numbering_str)
    
    # download protobin
    bucket_name = json_data["bucket_name"]
    relative_path = json_data["file_name"]
    task_name = json_data["task_name"]

    # Define file paths
    bos_p = f"bos:/{bucket_name}/auto-labeling/label/{task_name}/{version}/{relative_path}/labels/multi_task_label.prototxt"
    local_p = f"{root_dir}/label/{task_name}/{version}/{relative_path}/labels/multi_task_label.prototxt"

    bos_p_2 = f"bos:/{bucket_name}/auto-labeling/label/{task_name}/{version}/{relative_path}/labels/auto_labeling.prototxt"
    local_p_2 = f"{root_dir}/label/{task_name}/{version}/{relative_path}/labels/auto_labeling.prototxt"
    cmd = f"bcecmd bos cp -ry {bos_p} {local_p}"
    # Check if multi_task_label.prototxt exists locally
    if not os.path.exists(local_p):
        cmd = f"bcecmd bos cp -ry {bos_p} {local_p}"
        print(f"Downloading {bos_p} to {local_p}")
        os.system(cmd)
    else:
        print(f"{local_p} already exists. Skipping download.")

    # Check if auto_labeling.prototxt exists locally
    if not os.path.exists(local_p_2):
        cmd = f"bcecmd bos cp -ry {bos_p_2} {local_p_2}"
        print(f"Downloading {bos_p_2} to {local_p_2}")
        os.system(cmd)
    else:
        print(f"{local_p_2} already exists. Skipping download.")

    if not os.path.exists(local_p):
        return -1
    
    # with open(local_p, 'rb') as f:
    #     lines = f.read()
    # label_info = auto_labeling_label_pb2.LabelInfo()
    # label_info.ParseFromString(lines)
    try:
        '''multi_task_label.prototxt'''
        with open(local_p, 'rb') as f:
            lines = f.read()
        label_info = auto_labeling_label_pb2.LabelInfo()
        label_info.ParseFromString(lines)
        '''auto_labeling.prototxt'''
        # label_info = auto_labeling_label_pb2.LabelInfo()
        # with open(local_p_2, 'r') as f:
        #     text_format.Merge(f.read(), label_info, allow_unknown_field=True)
    except Exception as e:
        print(f"Binary format failed: {e}")
        print(local_p)
        return -1


    objects = label_info.auto_labeling_label.lidar_label[0].objects
    base_name = json_name.split(".")[0]
    txt_name = f"{base_name}.txt"
    txt_content = []
    for obj in objects:
        type_str = type_map[obj.type]
        truncated = 0.0
        occluded = float(obj.occ_ratio)
        size = obj.size.data
        center = obj.center.data
        velocity = obj.velocity.data
        track_id = int(obj.track_id)
        acceleration = obj.acceleration.data
        yaw_rate = float(obj.yaw_rate)
        front_wheel_steer_angle = math.radians(float(obj.front_wheel_steer_angle))
        front_wheel_steer_angle = (front_wheel_steer_angle + math.pi) % (2 * math.pi) - math.pi

        direction = obj.direction.data
        velocity = obj.velocity.data
        vx, vy = velocity[0], velocity[1]
        dx, dy = direction[0], direction[1]
        if vx == 0 and vy == 0:
            vel_dir_diff_angle = 0.0  
        else:
            vel_angle = math.atan2(vy, vx)
            direction_angle = math.atan2(dy, dx)
            vel_dir_diff_angle = vel_angle - direction_angle
            vel_dir_diff_angle = (vel_dir_diff_angle + math.pi) % (2 * math.pi) - math.pi

        alpha = 0.0
        bbox = f"0.0 0.0 0.0 0.0"
        direction = obj.direction.data
        yaw = np.arctan2(direction[1], direction[0])
        yaw = np.round(yaw - np.floor(yaw / (2 * np.pi) + 0.5) * (2 * np.pi), 8)
        ry = -yaw - np.pi / 2
        ry = ry - np.floor(ry / (2 * np.pi) + 0.5) * (2 * np.pi)
        
        dim_str = f"{size[2]} {size[1]} {size[0]}"
        loc_str = f"{center[0]} {center[2]} {center[1]}"
        vel_str = f"{velocity[0]} {velocity[1]} {velocity[2]}"
        acc_str = f"{acceleration[0]} {acceleration[1]} {acceleration[2]}"

        conf = obj.confidence
        motion_state = int(obj.motion_state)
        kitti_str = f"{type_str} {truncated} {occluded} {alpha} {bbox} {dim_str} "\
        f"{loc_str} {ry} {conf} {vel_str} {motion_state} {track_id} {acc_str} " \
        f"{front_wheel_steer_angle} {vel_dir_diff_angle} {yaw_rate}"
        txt_content.append(kitti_str)
    pred_dir = os.path.join(root_dir, "auto_labeling")
    os.makedirs(pred_dir, exist_ok=True)
    txt_p = os.path.join(pred_dir, txt_name)
    with open(txt_p, "w") as f:
        data = "\n".join(txt_content)
        f.write(data)
    return 0

def generate_qt(map_json_p, root_dir, version):
    data = json.load(open(map_json_p))
    data_p = Pool(1)
    res_list = []
    pbar = tqdm.tqdm(total=len(list(data.keys())))
    for json_name, json_data in data.items():
        # process_one_frame(root_dir, version, json_name, json_data)
        res = data_p.apply_async(process_one_frame, args=(root_dir, version, json_name, json_data),
                                callback=lambda _: pbar.update(1))
        res_list.append(res)
        
    data_p.close()
    data_p.join()
    for res in res_list:
        error_code = res.get()
        
if __name__ == "__main__":
    map_json_p = "gen_auto_labeling_result/roujiche/rt6_wuhan_acc_dec/assign/rt6_wuhan_acc_dec.json" # 第一步生成的对应json
    root_dir = "/home/<USER>/local_projects/baidu/personal-code/BAIDU_TOOLS_QSZ/gen_auto_labeling_result/roujiche/rt6_wuhan_acc_dec/prod_line"  # 存储路径
    version = "adfm_500.6.0.1_motion" # 自标版本
    generate_qt(map_json_p, root_dir, version)