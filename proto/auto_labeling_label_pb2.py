# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: auto_labeling_label.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='auto_labeling_label.proto',
  package='idg.perception.autolabeling',
  syntax='proto2',
  serialized_options=None,
  serialized_pb=_b('\n\x19\x61uto_labeling_label.proto\x12\x1bidg.perception.autolabeling\"\x17\n\x07VectorF\x12\x0c\n\x04\x64\x61ta\x18\x01 \x03(\x02\"\x17\n\x07VectorD\x12\x0c\n\x04\x64\x61ta\x18\x01 \x03(\x01\"2\n\x0bImgsizeInfo\x12\x11\n\x06height\x18\x01 \x01(\r:\x01\x30\x12\x10\n\x05width\x18\x02 \x01(\r:\x01\x30\"H\n\x06PointF\x12\x0c\n\x01x\x18\x01 \x01(\x02:\x01\x30\x12\x0c\n\x01y\x18\x02 \x01(\x02:\x01\x30\x12\x0c\n\x01z\x18\x03 \x01(\x02:\x01\x30\x12\x14\n\tintensity\x18\x04 \x01(\x02:\x01\x30\"H\n\x06PointD\x12\x0c\n\x01x\x18\x01 \x01(\x01:\x01\x30\x12\x0c\n\x01y\x18\x02 \x01(\x01:\x01\x30\x12\x0c\n\x01z\x18\x03 \x01(\x01:\x01\x30\x12\x14\n\tintensity\x18\x04 \x01(\x01:\x01\x30\"\xb6\x01\n\x0fPointFCloudInfo\x12\x33\n\x06points\x18\x01 \x03(\x0b\x32#.idg.perception.autolabeling.PointF\x12\x10\n\x05width\x18\x02 \x01(\r:\x01\x30\x12\x11\n\x06height\x18\x03 \x01(\r:\x01\x30\x12\x15\n\rpoints_height\x18\x04 \x03(\x02\x12\x18\n\x0cpoints_label\x18\x05 \x03(\rB\x02\x10\x01\x12\x18\n\x0cpoints_index\x18\x06 \x03(\x05\x42\x02\x10\x01\"=\n\x0bTranslation\x12\x0e\n\x01x\x18\x01 \x01(\x01:\x03nan\x12\x0e\n\x01y\x18\x02 \x01(\x01:\x03nan\x12\x0e\n\x01z\x18\x03 \x01(\x01:\x03nan\"P\n\nQuaternion\x12\x0f\n\x02qx\x18\x01 \x01(\x01:\x03nan\x12\x0f\n\x02qy\x18\x02 \x01(\x01:\x03nan\x12\x0f\n\x02qz\x18\x03 \x01(\x01:\x03nan\x12\x0f\n\x02qw\x18\x04 \x01(\x01:\x03nan\"\x86\x01\n\x08PoseInfo\x12=\n\x0btranslation\x18\x03 \x01(\x0b\x32(.idg.perception.autolabeling.Translation\x12;\n\nquaternion\x18\x04 \x01(\x0b\x32\'.idg.perception.autolabeling.Quaternion\"h\n\x0cPolygonDType\x12\x33\n\x06points\x18\x01 \x03(\x0b\x32#.idg.perception.autolabeling.PointD\x12\x10\n\x05width\x18\x02 \x01(\r:\x01\x30\x12\x11\n\x06height\x18\x03 \x01(\r:\x01\x30\"\xa0\x03\n\x0fLidarCameraInfo\x12\x1c\n\x0bsensor_name\x18\x01 \x01(\t:\x07UNKNOWN\x12\x19\n\x0ematch_distance\x18\x02 \x01(\x01:\x01\x30\x12\x17\n\x0cproject_area\x18\x03 \x01(\x01:\x01\x30\x12\x18\n\rincrease_area\x18\x04 \x01(\x01:\x01\x30\x12\x1a\n\x0fpts_inner_ratio\x18\x05 \x01(\x01:\x01\x30\x12\x16\n\x07visible\x18\x06 \x01(\x08:\x05\x66\x61lse\x12\x36\n\x08proj_box\x18\x07 \x01(\x0b\x32$.idg.perception.autolabeling.VectorF\x12\x37\n\timage_box\x18\x08 \x01(\x0b\x32$.idg.perception.autolabeling.VectorF\x12\x1b\n\x10image_confidence\x18\t \x01(\x01:\x01\x30\x12\x0e\n\x04type\x18\n \x01(\t:\x00\x12\x12\n\x08sub_type\x18\x0b \x01(\t:\x00\x12\x13\n\x08truncate\x18\x0c \x01(\x02:\x01\x31\x12\x14\n\tocc_ratio\x18\r \x01(\x02:\x01\x31\x12\x10\n\x06uid_2d\x18\x0e \x01(\t:\x00\"m\n\x10\x44\x61taCompleteness\x12\x16\n\x08\x63omplete\x18\x01 \x01(\x08:\x04true\x12\x41\n\x0elose_data_info\x18\x02 \x03(\x0e\x32).idg.perception.autolabeling.LoseDataInfo\"\xad\x01\n\x14MetricSupplementInfo\x12\x13\n\x07\x62ox_iou\x18\x01 \x01(\x02:\x02-1\x12\x16\n\npoints_iou\x18\x02 \x01(\x02:\x02-1\x12\x1a\n\x0e\x64istance_error\x18\x03 \x01(\x02:\x02-1\x12\x15\n\tbox_ratio\x18\x04 \x01(\x02:\x02-1\x12\x35\n\x07map_box\x18\x05 \x01(\x0b\x32$.idg.perception.autolabeling.VectorF\"\xdc\x03\n\x10\x46usionSupplement\x12\x16\n\nconfidence\x18\x01 \x01(\x02:\x02-1\x12\x18\n\x0c\x63onf_seq_min\x18\x02 \x01(\x02:\x02-1\x12\x18\n\x0c\x63onf_seq_mid\x18\x03 \x01(\x02:\x02-1\x12\x18\n\x0c\x63onf_seq_max\x18\x04 \x01(\x02:\x02-1\x12\x1a\n\x0e\x63onf_lidar_det\x18\x05 \x01(\x02:\x02-1\x12\x1c\n\x10\x63onf_lidar_parse\x18\x06 \x01(\x02:\x02-1\x12\x1b\n\x0f\x63onf_camera_det\x18\x07 \x01(\x02:\x02-1\x12\x1d\n\x11\x63onf_camera_parse\x18\x08 \x01(\x02:\x02-1\x12\x1d\n\x12num_foreground_pts\x18\t \x01(\r:\x01\x30\x12\x1c\n\x11num_pts_wo_ground\x18\n \x01(\r:\x01\x30\x12\x1f\n\x13\x63onf_seq_window_min\x18\x0b \x01(\x02:\x02-1\x12 \n\x14\x63onf_seq_window_mean\x18\x0c \x01(\x02:\x02-1\x12\x1f\n\x13\x63onf_seq_window_max\x18\r \x01(\x02:\x02-1\x12\x1a\n\x0ftracking_length\x18\x0e \x01(\r:\x01\x30\x12\x17\n\x0cpolygon_area\x18\x32 \x01(\x02:\x01\x30\x12\x16\n\x0bpolygon_pts\x18\x33 \x01(\r:\x01\x30\"\xb7\x01\n\x10\x43\x61meraSupplement\x12\x44\n\x0focclusion_state\x18\x01 \x01(\x0e\x32+.idg.perception.autolabeling.OcclusionState\x12\x18\n\tis_onroad\x18\x02 \x01(\x08:\x05\x66\x61lse\x12\x19\n\nis_ignored\x18\x03 \x01(\x08:\x05\x66\x61lse\x12\x15\n\ntruncation\x18\x04 \x01(\r:\x01\x30\x12\x11\n\x05\x61lpha\x18\x05 \x01(\x02:\x02-1\"\x80\x02\n\x0fLidarSupplement\x12\x1f\n\x10is_on_fence_area\x18\x01 \x01(\x08:\x05\x66\x61lse\x12\x1f\n\x10is_on_main_lanes\x18\x02 \x01(\x08:\x05\x66\x61lse\x12\x1f\n\x10is_swerve_bigmot\x18\x03 \x01(\x08:\x05\x66\x61lse\x12G\n\x11lidar_camera_info\x18\x04 \x03(\x0b\x32,.idg.perception.autolabeling.LidarCameraInfo\x12\x41\n\x0blocal_cloud\x18\x14 \x01(\x0b\x32,.idg.perception.autolabeling.PointFCloudInfo\"\xfd\x01\n\x16QualityCheckObjectInfo\x12\x1e\n\x0fmotion_state_nq\x18\x01 \x01(\x08:\x05\x66\x61lse\x12\x1a\n\x0bvelocity_nq\x18\x02 \x01(\x08:\x05\x66\x61lse\x12G\n\x11\x64\x65tection_qc_info\x18\x03 \x01(\x0e\x32,.idg.perception.autolabeling.DetectionQCInfo\x12\x1c\n\rasso_2d_3d_nq\x18\x05 \x01(\x08:\x05\x66\x61lse\x12 \n\x11\x61sso_2d_3d_update\x18\x06 \x01(\x08:\x05\x66\x61lse\x12\x1e\n\x0fneed_high_light\x18\x1e \x01(\x08:\x05\x66\x61lse\"\x8e\x01\n\x15QualityCheckFrameInfo\x12\x1e\n\x0fmotion_state_nq\x18\x01 \x01(\x08:\x05\x66\x61lse\x12\x1a\n\x0bvelocity_nq\x18\x02 \x01(\x08:\x05\x66\x61lse\x12\x1b\n\x0c\x64\x65tection_nq\x18\x03 \x01(\x08:\x05\x66\x61lse\x12\x1c\n\rasso_2d_3d_nq\x18\x04 \x01(\x08:\x05\x66\x61lse\"\xe4\x01\n\x1aTrafficLightSupplementInfo\x12\x11\n\x07map_uid\x18\x01 \x01(\t:\x00\x12\x13\n\x05\x61ngle\x18\x02 \x01(\x02:\x04-180\x12\x16\n\ntype_shape\x18\x03 \x01(\x05:\x02-1\x12\x1b\n\x0foccluded_degree\x18\x04 \x01(\x02:\x02-1\x12\x17\n\x0blanppost_id\x18\x05 \x01(\x02:\x02-1\x12\x1c\n\x10manual_direction\x18\x06 \x01(\x02:\x02-1\x12\x1e\n\x12occluded_direction\x18\x07 \x01(\x02:\x02-1\x12\x12\n\x06is_ped\x18\x08 \x01(\x05:\x02-1\"\xd9\x02\n\x10\x42igMotObjectInfo\x12\x62\n\nfit_status\x18\x01 \x01(\x0e\x32\x37.idg.perception.autolabeling.BigMotObjectInfo.FitStatus:\x15WithOutRearViewMirror\x12\x34\n\x06\x63\x65nter\x18\x02 \x01(\x0b\x32$.idg.perception.autolabeling.VectorD\x12\x32\n\x04size\x18\x03 \x01(\x0b\x32$.idg.perception.autolabeling.VectorF\x12\x37\n\tdirection\x18\x04 \x01(\x0b\x32$.idg.perception.autolabeling.VectorF\">\n\tFitStatus\x12\x16\n\x12WithRearViewMirror\x10\x00\x12\x19\n\x15WithOutRearViewMirror\x10\x01\"\'\n\x0bVisibleInfo\x12\x18\n\rvisible_ratio\x18\x01 \x01(\x02:\x01\x30\"\xd0\x01\n\x0cPreLabelInfo\x12\x1c\n\x10shape_confidence\x18\x01 \x01(\x02:\x02-1\x12\x19\n\nlabel_free\x18\x02 \x01(\x08:\x05\x66\x61lse\x12G\n\x10\x62ig_mot_obj_info\x18\n \x01(\x0b\x32-.idg.perception.autolabeling.BigMotObjectInfo\x12>\n\x0cvisible_info\x18\x0b \x01(\x0b\x32(.idg.perception.autolabeling.VisibleInfo\"\xd4\x03\n\x15ManualLabelObjectInfo\x12`\n\x0b\x63heck_state\x18\x01 \x01(\x0e\x32>.idg.perception.autolabeling.ManualLabelObjectInfo.CheckStatus:\x0bToBeChecked\x12`\n\x0blabel_state\x18\x02 \x01(\x0e\x32>.idg.perception.autolabeling.ManualLabelObjectInfo.CheckStatus:\x0bToBeChecked\x12\x1a\n\x0fnum_geometry_op\x18\x03 \x01(\r:\x01\x30\x12\x1a\n\x0fnum_property_op\x18\x04 \x01(\r:\x01\x30\x12\x62\n\rquality_state\x18\x05 \x01(\x0e\x32>.idg.perception.autolabeling.ManualLabelObjectInfo.CheckStatus:\x0bToBeChecked\x12 \n\x16meta_manual_label_info\x18\n \x01(\t:\x00\"9\n\x0b\x43heckStatus\x12\x0f\n\x0bToBeChecked\x10\x00\x12\x0b\n\x07Ignored\x10\x01\x12\x0c\n\x08\x46inished\x10\x02\".\n\x14ManualLabelFrameInfo\x12\x16\n\x07\x63hecked\x18\x01 \x01(\x08:\x05\x66\x61lse\"\xee\x03\n\x1bManualQualityInspectionInfo\x12\r\n\x02id\x18\x01 \x01(\r:\x01\x30\x12\x66\n\x0b\x63heck_state\x18\x02 \x01(\x0e\x32\x44.idg.perception.autolabeling.ManualQualityInspectionInfo.CheckStatus:\x0bToBeChecked\x12g\n\nerror_type\x18\x03 \x01(\x0e\x32\x42.idg.perception.autolabeling.ManualQualityInspectionInfo.ErrorType:\x0f\x45rrorType_Error\x12\x14\n\nerror_info\x18\x04 \x01(\t:\x00\x12\x14\n\nextra_info\x18\x05 \x01(\t:\x00\x12\x36\n\x08location\x18\x06 \x01(\x0b\x32$.idg.perception.autolabeling.VectorD\"9\n\x0b\x43heckStatus\x12\x0f\n\x0bToBeChecked\x10\x00\x12\x0b\n\x07Ignored\x10\x01\x12\x0c\n\x08\x46inished\x10\x02\"P\n\tErrorType\x12\x15\n\x11\x45rrorType_Missing\x10\x00\x12\x13\n\x0f\x45rrorType_Error\x10\x01\x12\x17\n\x13\x45rrorType_Redundant\x10\x02\"\xa4\x01\n\nDoorStatus\x12\x18\n\tdoor_open\x18\x01 \x01(\x08:\x05\x66\x61lse\x12\x1d\n\x0eleft_door_open\x18\x02 \x01(\x08:\x05\x66\x61lse\x12\x1e\n\x0fright_door_open\x18\x03 \x01(\x08:\x05\x66\x61lse\x12\x1e\n\x0f\x66ront_door_open\x18\x04 \x01(\x08:\x05\x66\x61lse\x12\x1d\n\x0e\x62\x61\x63k_door_open\x18\x05 \x01(\x08:\x05\x66\x61lse\"\xd3\x01\n\x0bLightSignal\x12H\n\x06status\x18\x01 \x01(\x0e\x32/.idg.perception.autolabeling.LightSignal.Status:\x07UNKNOWN\x12\x15\n\nconfidence\x18\x02 \x01(\x02:\x01\x30\x12\x17\n\x0cvisible_prob\x18\x03 \x01(\x02:\x01\x30\x12\x19\n\x0eswitch_on_prob\x18\x04 \x01(\x02:\x01\x30\"/\n\x06Status\x12\x14\n\x07UNKNOWN\x10\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\x12\x07\n\x03OFF\x10\x00\x12\x06\n\x02ON\x10\x01\"\xd8\x01\n\x0eReversingTimes\x12\x1c\n\x0f\x63\x61se_start_time\x18\x01 \x01(\x01:\x03nan\x12\x1a\n\rcase_end_time\x18\x02 \x01(\x01:\x03nan\x12!\n\x14reversing_start_time\x18\x03 \x01(\x01:\x03nan\x12\"\n\x15reversing_static_time\x18\x04 \x01(\x01:\x03nan\x12$\n\x17reversing_backward_time\x18\x05 \x01(\x01:\x03nan\x12\x1f\n\x12reversing_end_time\x18\x06 \x01(\x01:\x03nan\"\xf5\x01\n\x0fReversingStatus\x12\x44\n\x04type\x18\x01 \x01(\x0e\x32*.idg.perception.autolabeling.ReversingType:\nRT_UNKNOWN\x12L\n\x08sub_type\x18\x02 \x01(\x0e\x32-.idg.perception.autolabeling.ReversingSubType:\x0bRST_UNKNOWN\x12:\n\x05times\x18\x03 \x01(\x0b\x32+.idg.perception.autolabeling.ReversingTimes\x12\x12\n\nconfidence\x18\x04 \x01(\x01\"\xa2\x01\n\x0e\x43rossingStatus\x12\x43\n\x04type\x18\x01 \x01(\x0e\x32).idg.perception.autolabeling.CrossingType:\nCT_UNKNOWN\x12K\n\x08sub_type\x18\x02 \x01(\x0e\x32,.idg.perception.autolabeling.CrossingSubType:\x0b\x43ST_UNKNOWN\"\x9a\x1e\n\nObjectInfo\x12\x15\n\x0bsensor_name\x18\x01 \x01(\t:\x00\x12\x0e\n\x02id\x18\x02 \x01(\x05:\x02-1\x12\r\n\x03uid\x18\x03 \x01(\t:\x00\x12\x0e\n\x04type\x18\x04 \x01(\t:\x00\x12\x12\n\x08sub_type\x18\x05 \x01(\t:\x00\x12\x16\n\nconfidence\x18\x06 \x01(\x02:\x02-1\x12\x31\n\x03\x62ox\x18\x07 \x01(\x0b\x32$.idg.perception.autolabeling.VectorF\x12\x34\n\x06\x63\x65nter\x18\x08 \x01(\x0b\x32$.idg.perception.autolabeling.VectorD\x12\x32\n\x04size\x18\t \x01(\x0b\x32$.idg.perception.autolabeling.VectorF\x12\x37\n\tdirection\x18\n \x01(\x0b\x32$.idg.perception.autolabeling.VectorF\x12\x36\n\x08velocity\x18\x0b \x01(\x0b\x32$.idg.perception.autolabeling.VectorF\x12:\n\x0c\x61\x63\x63\x65leration\x18) \x01(\x0b\x32$.idg.perception.autolabeling.VectorF\x12\x13\n\x08yaw_rate\x18* \x01(\x02:\x01\x30\x12\"\n\x17\x66ront_wheel_steer_angle\x18+ \x01(\x02:\x01\x30\x12\x1d\n\x12vel_dir_diff_angle\x18, \x01(\x02:\x01\x30\x12\x44\n\tturn_type\x18- \x01(\x0e\x32%.idg.perception.autolabeling.TurnType:\nTT_UNKNOWN\x12<\n\rorigin_center\x18\x8c\x01 \x01(\x0b\x32$.idg.perception.autolabeling.VectorD\x12:\n\x0borigin_size\x18\x8d\x01 \x01(\x0b\x32$.idg.perception.autolabeling.VectorF\x12?\n\x10origin_direction\x18\x8e\x01 \x01(\x0b\x32$.idg.perception.autolabeling.VectorF\x12\x16\n\x0borigin_type\x18\x8f\x01 \x01(\t:\x00\x12\x1a\n\x0forigin_sub_type\x18\x90\x01 \x01(\t:\x00\x12R\n\troi_state\x18\x0c \x01(\x0e\x32).idg.perception.autolabeling.ROIStateInfo:\x14ROIStateInfo_UNKNOWN\x12W\n\x0euser_roi_state\x18y \x01(\x0e\x32).idg.perception.autolabeling.ROIStateInfo:\x14ROIStateInfo_UNKNOWN\x12[\n\x0cmotion_state\x18\r \x01(\x0e\x32,.idg.perception.autolabeling.MotionStateInfo:\x17MotionStateInfo_UNKNOWN\x12\x1f\n\x13velocity_confidence\x18\x0e \x01(\x02:\x02-1\x12\x1b\n\x0f\x63rispness_score\x18\x0f \x01(\x02:\x02-1\x12\x1c\n\x10pts_count_inside\x18\x10 \x01(\x05:\x02-1\x12\x14\n\x08track_id\x18\x11 \x01(\x05:\x02-1\x12\x13\n\ttrack_uid\x18\x12 \x01(\t:\x00\x12\x19\n\tlabel_src\x18\x13 \x01(\t:\x06manual\x12:\n\x07polygon\x18\x14 \x01(\x0b\x32).idg.perception.autolabeling.PolygonDType\x12\x38\n\ntight_size\x18\x15 \x01(\x0b\x32$.idg.perception.autolabeling.VectorF\x12L\n\x11metric_supplement\x18\x1e \x01(\x0b\x32\x31.idg.perception.autolabeling.MetricSupplementInfo\x12Y\n\x18traffic_light_supplement\x18\x1f \x01(\x0b\x32\x37.idg.perception.autolabeling.TrafficLightSupplementInfo\x12R\n\x17metric_debug_supplement\x18  \x03(\x0b\x32\x31.idg.perception.autolabeling.MetricSupplementInfo\x12\x46\n\x10lidar_supplement\x18\x32 \x01(\x0b\x32,.idg.perception.autolabeling.LidarSupplement\x12H\n\x11\x63\x61mera_supplement\x18\x33 \x01(\x0b\x32-.idg.perception.autolabeling.CameraSupplement\x12H\n\x11\x66usion_supplement\x18\x34 \x01(\x0b\x32-.idg.perception.autolabeling.FusionSupplement\x12@\n\x07sv_type\x18\x35 \x01(\x0e\x32/.idg.perception.autolabeling.SpecialVehicleType\x12\x46\n\x15pedestrian_profession\x18\x36 \x01(\x0e\x32\'.idg.perception.autolabeling.Profession\x12<\n\x0b\x64oor_status\x18\x37 \x01(\x0b\x32\'.idg.perception.autolabeling.DoorStatus\x12K\n\x13\x66\x61\x63\x65_towards_status\x18\x38 \x01(\x0e\x32..idg.perception.autolabeling.FaceTowardsStatus\x12>\n\x0c\x62rake_signal\x18\x39 \x01(\x0b\x32(.idg.perception.autolabeling.LightSignal\x12\x42\n\x10left_turn_signal\x18: \x01(\x0b\x32(.idg.perception.autolabeling.LightSignal\x12\x43\n\x11right_turn_signal\x18; \x01(\x0b\x32(.idg.perception.autolabeling.LightSignal\x12K\n\x19\x65mergency_flashers_signal\x18< \x01(\x0b\x32(.idg.perception.autolabeling.LightSignal\x12>\n\x0conoff_signal\x18= \x01(\x0b\x32(.idg.perception.autolabeling.LightSignal\x12\x42\n\x10reversing_signal\x18> \x01(\x0b\x32(.idg.perception.autolabeling.LightSignal\x12\x42\n\x10operating_signal\x18? \x01(\x0b\x32(.idg.perception.autolabeling.LightSignal\x12\x41\n\x0epre_label_info\x18P \x01(\x0b\x32).idg.perception.autolabeling.PreLabelInfo\x12K\n\x0eqc_object_info\x18\x64 \x01(\x0b\x32\x33.idg.perception.autolabeling.QualityCheckObjectInfo\x12J\n\x0eml_object_info\x18x \x01(\x0b\x32\x32.idg.perception.autolabeling.ManualLabelObjectInfo\x12\x12\n\x08\x63ombo_id\x18z \x01(\t:\x00\x12\x45\n\nlight_head\x18{ \x03(\x0b\x32\x31.idg.perception.autolabeling.TrafficLightHeadInfo\x12@\n\x12velocity_direction\x18| \x01(\x0b\x32$.idg.perception.autolabeling.VectorF\x12\x15\n\x06target\x18} \x01(\x08:\x05\x66\x61lse\x12\x19\n\nregistered\x18~ \x01(\x08:\x05\x66\x61lse\x12>\n\x0fregistered_pose\x18\x7f \x01(\x0b\x32%.idg.perception.autolabeling.PoseInfo\x12Q\n\x0btarget_type\x18\x80\x01 \x01(\x0e\x32\'.idg.perception.autolabeling.TargetType:\x12TargetType_UNKNOWN\x12#\n\x14tramcar_is_occlusion\x18\x81\x01 \x01(\t:\x04none\x12V\n\x14\x62lindspot_supplement\x18\x82\x01 \x01(\x0b\x32\x37.idg.perception.autolabeling.BlindSpotObsSupplementInfo\x12%\n\x16\x61\x63\x63\x65ssory_is_occlusion\x18\x83\x01 \x01(\t:\x04none\x12#\n\x16\x61ssociated_obstacle_id\x18\x84\x01 \x01(\x05:\x02-1\x12\x1d\n\x12semantic_attribute\x18\x85\x01 \x01(\t:\x00\x12\x15\n\tocc_ratio\x18\x86\x01 \x01(\x02:\x01\x31\x12!\n\x14velocity_lower_bound\x18\x87\x01 \x01(\x02:\x02-1\x12!\n\x14velocity_upper_bound\x18\x88\x01 \x01(\x02:\x02-1\x12\x62\n\x16road_grid_height_level\x18\x89\x01 \x01(\x0e\x32\x32.idg.perception.autolabeling.AbRoadGridHeightLevel:\rLEVEL_UNKNOWN\x12^\n\x14road_grid_view_state\x18\x8a\x01 \x01(\x0e\x32\x30.idg.perception.autolabeling.AbRoadGridViewState:\rSTATE_UNKNOWN\x12G\n\x10reversing_status\x18\xaa\x01 \x01(\x0b\x32,.idg.perception.autolabeling.ReversingStatus\x12\x45\n\x0f\x63rossing_status\x18\xab\x01 \x01(\x0b\x32+.idg.perception.autolabeling.CrossingStatus\"`\n\x13\x42lindSpotPointFInfo\x12\x0f\n\x07pixel_x\x18\x01 \x01(\r\x12\x0f\n\x07pixel_y\x18\x02 \x01(\r\x12\x12\n\npoint_type\x18\x03 \x01(\t\x12\x13\n\x0bpoint_score\x18\x04 \x01(\x02\"e\n\x1a\x42lindSpotObsSupplementInfo\x12G\n\rground_points\x18\x01 \x03(\x0b\x32\x30.idg.perception.autolabeling.BlindSpotPointFInfo\"\xe9\x01\n\x17ReconstructedObjectInfo\x12\x14\n\x08track_id\x18\x01 \x01(\x05:\x02-1\x12\x13\n\ttrack_uid\x18\x02 \x01(\t:\x00\x12[\n\x0cmotion_state\x18\x03 \x01(\x0e\x32,.idg.perception.autolabeling.MotionStateInfo:\x17MotionStateInfo_UNKNOWN\x12\x12\n\x08pcd_path\x18\x04 \x01(\t:\x00\x12\x1c\n\x10\x62\x61se_frame_index\x18\x05 \x01(\x05:\x02-1\x12\x14\n\x0c\x66rame_indexs\x18\x06 \x03(\x05\"\xa5\x02\n\x14TrafficLightHeadInfo\x12\x10\n\x04type\x18\x01 \x01(\x05:\x02-1\x12\x31\n\x03\x62ox\x18\x02 \x01(\x0b\x32$.idg.perception.autolabeling.VectorF\x12\x15\n\ttype_conf\x18\x03 \x01(\x02:\x02-1\x12\x1b\n\x0flighthead_color\x18\x04 \x01(\x05:\x02-1\x12 \n\x14lighthead_color_conf\x18\x05 \x01(\x02:\x02-1\x12\x17\n\x0buints_digit\x18\x06 \x01(\x05:\x02-1\x12\x16\n\ntens_digit\x18\x07 \x01(\x05:\x02-1\x12\x17\n\x0blight_color\x18\x08 \x01(\x05:\x02-1\x12\x19\n\tlabel_src\x18\t \x01(\t:\x06manual\x12\r\n\x03uid\x18\n \x01(\t:\x00\"\xce\x01\n\x06Header\x12\x0f\n\x07version\x18\x01 \x02(\t\x12\x11\n\tdate_time\x18\x02 \x02(\t\x12\x10\n\x08\x63omments\x18\x03 \x01(\t\x12\x0f\n\x07task_id\x18\x04 \x01(\t\x12\x13\n\x0bmap_version\x18\x05 \x01(\t\x12\x1f\n\x17online_lidar_model_name\x18\x06 \x01(\t\x12\"\n\x14offline_slam_version\x18\x07 \x01(\t:\x04None\x12#\n\x15offline_calib_version\x18\x08 \x01(\t:\x04None\"X\n\x14LidarLabelMerticInfo\x12$\n\x18manual_contributed_ratio\x18\x01 \x01(\x02:\x02-1\x12\x1a\n\x0eparsing_metric\x18\x02 \x03(\x02\x42\x02\x10\x01\"\xcb\x01\n\nParseLabel\x12\x16\n\ncls_parser\x18\x01 \x03(\rB\x02\x10\x01\x12\x13\n\x07\x63ls_sbr\x18\x02 \x03(\rB\x02\x10\x01\x12\x0e\n\x02\x64x\x18\x03 \x03(\x02\x42\x02\x10\x01\x12\x0e\n\x02\x64y\x18\x04 \x03(\x02\x42\x02\x10\x01\x12\x0e\n\x02\x64z\x18\x05 \x03(\x02\x42\x02\x10\x01\x12\x12\n\x06in_roi\x18\x06 \x03(\x08\x42\x02\x10\x01\x12\x11\n\x05index\x18\x07 \x03(\x04\x42\x02\x10\x01\x12\x1d\n\x11\x63onfidence_parser\x18\x08 \x03(\rB\x02\x10\x01\x12\x1a\n\x0e\x63onfidence_sbr\x18\t \x03(\rB\x02\x10\x01\"\xc0\x07\n\x0eLidarLabelInfo\x12\x13\n\x0bsensor_name\x18\x01 \x02(\t\x12\x38\n\x07objects\x18\x02 \x03(\x0b\x32\'.idg.perception.autolabeling.ObjectInfo\x12\x11\n\x05label\x18\x03 \x03(\rB\x02\x10\x01\x12?\n\x10lidar2world_pose\x18\x04 \x01(\x0b\x32%.idg.perception.autolabeling.PoseInfo\x12\x14\n\x0ctimestamp_ns\x18\x05 \x01(\x04\x12<\n\x0bparse_label\x18\x06 \x01(\x0b\x32\'.idg.perception.autolabeling.ParseLabel\x12<\n\x0b\x64\x65nse_label\x18\x07 \x01(\x0b\x32\'.idg.perception.autolabeling.ParseLabel\x12G\n\x18lidar2world_pose_offline\x18\x08 \x01(\x0b\x32%.idg.perception.autolabeling.PoseInfo\x12?\n\x10lidar2local_pose\x18\t \x01(\x0b\x32%.idg.perception.autolabeling.PoseInfo\x12\x45\n\x13global_world_offset\x18\n \x01(\x0b\x32(.idg.perception.autolabeling.Translation\x12\x1d\n\x15online_location_score\x18\x0b \x01(\x01\x12\x1e\n\x16offline_location_score\x18\x0c \x01(\x01\x12\x12\n\x08json_key\x18\x1d \x01(\t:\x00\x12\x41\n\x06metric\x18\x1e \x01(\x0b\x32\x31.idg.perception.autolabeling.LidarLabelMerticInfo\x12\x43\n\x07qc_info\x18\x64 \x01(\x0b\x32\x32.idg.perception.autolabeling.QualityCheckFrameInfo\x12\x42\n\x07ml_info\x18x \x01(\x0b\x32\x31.idg.perception.autolabeling.ManualLabelFrameInfo\x12=\n\x0cobject_areas\x18y \x03(\x0b\x32\'.idg.perception.autolabeling.ObjectInfo\x12J\n\x08qi_infos\x18z \x03(\x0b\x32\x38.idg.perception.autolabeling.ManualQualityInspectionInfo\"\xa2\x01\n\x15\x43\x61meraLabelMerticInfo\x12\x19\n\rpercent_label\x18\x01 \x01(\x02:\x02-1\x12\x17\n\x0bpercent_map\x18\x02 \x01(\x02:\x02-1\x12\x1c\n\x10label_lights_num\x18\x03 \x01(\x02:\x02-1\x12\x1a\n\x0emap_lights_num\x18\x04 \x01(\x02:\x02-1\x12\x1b\n\x0fpair_lights_num\x18\x05 \x01(\x02:\x02-1\"\x8c\x05\n\x0f\x43\x61meraLabelInfo\x12\x13\n\x0bsensor_name\x18\x01 \x02(\t\x12\x38\n\x07objects\x18\x02 \x03(\x0b\x32\'.idg.perception.autolabeling.ObjectInfo\x12?\n\x0etraffic_lights\x18\x03 \x03(\x0b\x32\'.idg.perception.autolabeling.ObjectInfo\x12\x11\n\x05label\x18\x04 \x03(\rB\x02\x10\x01\x12\x14\n\x0ctimestamp_ns\x18\x05 \x01(\x04\x12@\n\x11\x63\x61mera2world_pose\x18\x06 \x01(\x0b\x32%.idg.perception.autolabeling.PoseInfo\x12\x1a\n\x0cis_undistort\x18\x1c \x01(\x08:\x04true\x12\x12\n\x08json_key\x18\x1d \x01(\t:\x00\x12\x39\n\x07imgsize\x18\x1e \x01(\x0b\x32(.idg.perception.autolabeling.ImgsizeInfo\x12\x42\n\x06metric\x18\x1f \x01(\x0b\x32\x32.idg.perception.autolabeling.CameraLabelMerticInfo\x12G\n\x0e\x62lindspot_info\x18( \x01(\x0b\x32/.idg.perception.autolabeling.BlindSpotFrameInfo\x12\x43\n\x07qc_info\x18\x64 \x01(\x0b\x32\x32.idg.perception.autolabeling.QualityCheckFrameInfo\x12\x41\n\x0bimg_qc_info\x18\x96\x01 \x01(\x0b\x32+.idg.perception.autolabeling.ImgQualityInfo\"`\n\x12\x42lindSpotFrameInfo\x12J\n\x10\x66reespace_points\x18\x01 \x03(\x0b\x32\x30.idg.perception.autolabeling.BlindSpotPointFInfo\"s\n\x0eImgQualityInfo\x12\x13\n\x0bnum_patches\x18\x01 \x01(\r\x12L\n\x0eregion_qc_info\x18\x02 \x03(\x0b\x32\x34.idg.perception.autolabeling.ImgSubRegionQualityInfo\"Y\n\x17ImgSubRegionQualityInfo\x12\x13\n\x0bpatch_index\x18\x01 \x01(\r\x12\x12\n\ndirty_type\x18\x02 \x01(\t\x12\x15\n\rquality_score\x18\x03 \x01(\x02\"\x97\x02\n\x15\x41utoLabelingLabelInfo\x12\x38\n\x07objects\x18\x01 \x03(\x0b\x32\'.idg.perception.autolabeling.ObjectInfo\x12@\n\x0blidar_label\x18\x02 \x03(\x0b\x32+.idg.perception.autolabeling.LidarLabelInfo\x12\x42\n\x0c\x63\x61mera_label\x18\x03 \x03(\x0b\x32,.idg.perception.autolabeling.CameraLabelInfo\x12>\n\tbev_label\x18\x04 \x01(\x0b\x32+.idg.perception.autolabeling.LidarLabelInfo\"\xa7\x01\n\tFrameInfo\x12!\n\x12is_blind_spot_miss\x18\x01 \x01(\x08:\x05\x66\x61lse\x12\x16\n\x07qc_fail\x18\x02 \x01(\x08:\x05\x66\x61lse\x12>\n\rqc_fail_types\x18\x03 \x03(\x0e\x32\'.idg.perception.autolabeling.QcFailType\x12\x1f\n\x10\x66\x61r_cipv_missing\x18\x04 \x01(\x08:\x05\x66\x61lse\"\xf9\x03\n\tLabelInfo\x12\x33\n\x06header\x18\x01 \x02(\x0b\x32#.idg.perception.autolabeling.Header\x12@\n\x0blidar_label\x18\x02 \x03(\x0b\x32+.idg.perception.autolabeling.LidarLabelInfo\x12\x42\n\x0c\x63\x61mera_label\x18\x03 \x03(\x0b\x32,.idg.perception.autolabeling.CameraLabelInfo\x12O\n\x13\x61uto_labeling_label\x18\x04 \x01(\x0b\x32\x32.idg.perception.autolabeling.AutoLabelingLabelInfo\x12\x45\n\nlabel_type\x18\x05 \x01(\x0e\x32&.idg.perception.autolabeling.LabelType:\tFullLabel\x12H\n\x11\x64\x61ta_completeness\x18\x06 \x01(\x0b\x32-.idg.perception.autolabeling.DataCompleteness\x12:\n\nframe_info\x18\x07 \x01(\x0b\x32&.idg.perception.autolabeling.FrameInfo\x12\x13\n\tmeta_info\x18\x14 \x01(\t:\x00\"^\n\nLabelInfos\x12\x14\n\x0ctimestamp_ns\x18\x01 \x03(\x04\x12:\n\nlabel_info\x18\x02 \x03(\x0b\x32&.idg.perception.autolabeling.LabelInfo*[\n\x0cROIStateInfo\x12\x18\n\x14ROIStateInfo_UNKNOWN\x10\x00\x12\x17\n\x13ROIStateInfo_IN_ROI\x10\x01\x12\x18\n\x14ROIStateInfo_OUT_ROI\x10\x02*\x89\x01\n\x0fMotionStateInfo\x12\x1b\n\x17MotionStateInfo_UNKNOWN\x10\x00\x12\x1a\n\x16MotionStateInfo_MOVING\x10\x01\x12\x1e\n\x1aMotionStateInfo_STATIONARY\x10\x02\x12\x1d\n\x19MotionStateInfo_UNCERTAIN\x10\x03*)\n\tLabelType\x12\r\n\tFullLabel\x10\x00\x12\r\n\tWeakLabel\x10\x01*;\n\x0cLoseDataInfo\x12\r\n\tLoseLidar\x10\x00\x12\r\n\tLoseImage\x10\x01\x12\r\n\tLoseParam\x10\x02*v\n\x0f\x44\x65tectionQCInfo\x12\x1b\n\x17\x44\x65tectionQCInfo_UNKNOWN\x10\x00\x12\x16\n\x12\x44\x65tectionQCInfo_FN\x10\x01\x12\x16\n\x12\x44\x65tectionQCInfo_FP\x10\x02\x12\x16\n\x12\x44\x65tectionQCInfo_TP\x10\x03*u\n\x0eOcclusionState\x12\x15\n\x11OcclusionState_No\x10\x00\x12\x17\n\x13OcclusionState_Part\x10\x01\x12\x1b\n\x17OcclusionState_Complete\x10\x02\x12\x16\n\x12OcclusionState_Max\x10\x03*\xb4\x01\n\nTargetType\x12\x16\n\x12TargetType_UNKNOWN\x10\x00\x12\x15\n\x11TargetType_BigMot\x10\x01\x12\x12\n\x0eTargetType_VRU\x10\x02\x12\x1b\n\x17TargetType_TrafficLight\x10\x03\x12\x15\n\x11TargetType_LowObs\x10\x04\x12\x16\n\x12TargetType_Barrier\x10\x05\x12\x17\n\x13TargetType_SmallMot\x10\x06*\x99\x02\n\x12SpecialVehicleType\x12\x0b\n\x07SV_NONE\x10\x00\x12\x15\n\x11SV_POLICE_VEHICLE\x10\x01\x12\x18\n\x14SV_POLICE_MOTORCYCLE\x10\x02\x12\x12\n\x0eSV_FIRE_ENGINE\x10\x03\x12\x10\n\x0cSV_AMBULANCE\x10\x04\x12\x1a\n\x16SV_ENGINEERING_VEHICLE\x10\x05\x12\x11\n\rSV_SCHOOL_BUS\x10\x06\x12\n\n\x06SV_BUS\x10\x07\x12\x10\n\x0cSV_SPRINKLER\x10\x08\x12\x1e\n\x1aSV_SPRINKLER_WITHOUT_BRUSH\x10\t\x12\x17\n\x13SV_TRUCK_WITH_SLOPE\x10\n\x12\x19\n\x15SV_SANITATION_VEHICLE\x10\x0b*N\n\nProfession\x12\x18\n\x14POINTLESS_PROFESSION\x10\x00\x12\x1a\n\x16\x43ONSTRUCTOR_OR_CLEANER\x10\x01\x12\n\n\x06POLICE\x10\x02*B\n\x11\x46\x61\x63\x65TowardsStatus\x12\x0f\n\x0b\x46TS_UNKNOWN\x10\x00\x12\r\n\tFTS_FRONT\x10\x01\x12\r\n\tFTS_OTHER\x10\x02*\xc9\x01\n\rReversingType\x12\x0e\n\nRT_UNKNOWN\x10\x00\x12\x14\n\x10RT_NOT_REVERSING\x10\x01\x12\x17\n\x13RT_NORMAL_REVERSING\x10\x02\x12\x17\n\x13RT_SAME_THREE_POINT\x10\x03\x12\x1b\n\x17RT_OPPOSITE_THREE_POINT\x10\x04\x12\x17\n\x13RT_PARALLEL_PARKING\x10\x05\x12\x1b\n\x17RT_PARALLEL_PARKING_OUT\x10\x06\x12\r\n\tRT_IGNORE\x10\x07*V\n\x10ReversingSubType\x12\x0f\n\x0bRST_UNKNOWN\x10\x00\x12\x0f\n\x0bRST_FORWARD\x10\x01\x12\x0e\n\nRST_STATIC\x10\x02\x12\x10\n\x0cRST_BACKWARD\x10\x03*S\n\x0c\x43rossingType\x12\x0e\n\nCT_UNKNOWN\x10\x00\x12\x13\n\x0f\x43T_NOT_CROSSING\x10\x01\x12\x0f\n\x0b\x43T_CROSSING\x10\x02\x12\r\n\tCT_IGNORE\x10\x03*\xe6\x01\n\x0f\x43rossingSubType\x12\x0f\n\x0b\x43ST_UNKNOWN\x10\x00\x12\x17\n\x13\x43ST_NORMAL_CROSSING\x10\x01\x12\x1a\n\x16\x43ST_SIDE_TURN_CROSSING\x10\x02\x12\x18\n\x14\x43ST_WAITING_CROSSING\x10\x03\x12\x1d\n\x19\x43ST_PARALLEL_NOT_CROSSING\x10\x04\x12\x1b\n\x17\x43ST_DETOUR_NOT_CROSSING\x10\x05\x12\x1b\n\x17\x43ST_STATIC_NOT_CROSSING\x10\x06\x12\x1a\n\x16\x43ST_OTHER_NOT_CROSSING\x10\x07*;\n\x08TurnType\x12\x0e\n\nTT_UNKNOWN\x10\x00\x12\x0f\n\x0bTT_STRAIGHT\x10\x01\x12\x0e\n\nTT_TURNING\x10\x02*\xa0\x01\n\x15\x41\x62RoadGridHeightLevel\x12\x11\n\rLEVEL_UNKNOWN\x10\x00\x12\x0e\n\nLEVEL_FLAT\x10\x01\x12\x0e\n\nLEVEL_UP_1\x10\x02\x12\x0e\n\nLEVEL_UP_2\x10\x03\x12\x0e\n\nLEVEL_UP_3\x10\x04\x12\x10\n\x0cLEVEL_DOWN_1\x10\x05\x12\x10\n\x0cLEVEL_DOWN_2\x10\x06\x12\x10\n\x0cLEVEL_DOWN_3\x10\x07*O\n\x13\x41\x62RoadGridViewState\x12\x11\n\rSTATE_UNKNOWN\x10\x00\x12\x12\n\x0eSTATE_OUT_VIEW\x10\x01\x12\x11\n\rSTATE_IN_VIEW\x10\x02*\x89\x02\n\nQcFailType\x12\x16\n\x12QcFailType_UNKNOWN\x10\x01\x12!\n\x1dQcFailType_FG_ONLINE_MODEL_FN\x10\x02\x12!\n\x1dQcFailType_FG_ONLINE_MODEL_FP\x10\x03\x12\x1e\n\x1aQcFailType_FG_BIG_MODEL_FN\x10\x04\x12\x1e\n\x1aQcFailType_FG_BIG_MODEL_FP\x10\x05\x12%\n!QcFailType_FG_ONLINE_MODEL_KEY_FN\x10\x06\x12\x1a\n\x16QcFailType_BG_NOISE_FN\x10\x07\x12\x1a\n\x16QcFailType_BG_NOISE_FP\x10\x08')
)

_ROISTATEINFO = _descriptor.EnumDescriptor(
  name='ROIStateInfo',
  full_name='idg.perception.autolabeling.ROIStateInfo',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='ROIStateInfo_UNKNOWN', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ROIStateInfo_IN_ROI', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ROIStateInfo_OUT_ROI', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=14024,
  serialized_end=14115,
)
_sym_db.RegisterEnumDescriptor(_ROISTATEINFO)

ROIStateInfo = enum_type_wrapper.EnumTypeWrapper(_ROISTATEINFO)
_MOTIONSTATEINFO = _descriptor.EnumDescriptor(
  name='MotionStateInfo',
  full_name='idg.perception.autolabeling.MotionStateInfo',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='MotionStateInfo_UNKNOWN', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='MotionStateInfo_MOVING', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='MotionStateInfo_STATIONARY', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='MotionStateInfo_UNCERTAIN', index=3, number=3,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=14118,
  serialized_end=14255,
)
_sym_db.RegisterEnumDescriptor(_MOTIONSTATEINFO)

MotionStateInfo = enum_type_wrapper.EnumTypeWrapper(_MOTIONSTATEINFO)
_LABELTYPE = _descriptor.EnumDescriptor(
  name='LabelType',
  full_name='idg.perception.autolabeling.LabelType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='FullLabel', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='WeakLabel', index=1, number=1,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=14257,
  serialized_end=14298,
)
_sym_db.RegisterEnumDescriptor(_LABELTYPE)

LabelType = enum_type_wrapper.EnumTypeWrapper(_LABELTYPE)
_LOSEDATAINFO = _descriptor.EnumDescriptor(
  name='LoseDataInfo',
  full_name='idg.perception.autolabeling.LoseDataInfo',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='LoseLidar', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='LoseImage', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='LoseParam', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=14300,
  serialized_end=14359,
)
_sym_db.RegisterEnumDescriptor(_LOSEDATAINFO)

LoseDataInfo = enum_type_wrapper.EnumTypeWrapper(_LOSEDATAINFO)
_DETECTIONQCINFO = _descriptor.EnumDescriptor(
  name='DetectionQCInfo',
  full_name='idg.perception.autolabeling.DetectionQCInfo',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='DetectionQCInfo_UNKNOWN', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DetectionQCInfo_FN', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DetectionQCInfo_FP', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DetectionQCInfo_TP', index=3, number=3,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=14361,
  serialized_end=14479,
)
_sym_db.RegisterEnumDescriptor(_DETECTIONQCINFO)

DetectionQCInfo = enum_type_wrapper.EnumTypeWrapper(_DETECTIONQCINFO)
_OCCLUSIONSTATE = _descriptor.EnumDescriptor(
  name='OcclusionState',
  full_name='idg.perception.autolabeling.OcclusionState',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='OcclusionState_No', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OcclusionState_Part', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OcclusionState_Complete', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OcclusionState_Max', index=3, number=3,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=14481,
  serialized_end=14598,
)
_sym_db.RegisterEnumDescriptor(_OCCLUSIONSTATE)

OcclusionState = enum_type_wrapper.EnumTypeWrapper(_OCCLUSIONSTATE)
_TARGETTYPE = _descriptor.EnumDescriptor(
  name='TargetType',
  full_name='idg.perception.autolabeling.TargetType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='TargetType_UNKNOWN', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TargetType_BigMot', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TargetType_VRU', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TargetType_TrafficLight', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TargetType_LowObs', index=4, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TargetType_Barrier', index=5, number=5,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TargetType_SmallMot', index=6, number=6,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=14601,
  serialized_end=14781,
)
_sym_db.RegisterEnumDescriptor(_TARGETTYPE)

TargetType = enum_type_wrapper.EnumTypeWrapper(_TARGETTYPE)
_SPECIALVEHICLETYPE = _descriptor.EnumDescriptor(
  name='SpecialVehicleType',
  full_name='idg.perception.autolabeling.SpecialVehicleType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='SV_NONE', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SV_POLICE_VEHICLE', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SV_POLICE_MOTORCYCLE', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SV_FIRE_ENGINE', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SV_AMBULANCE', index=4, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SV_ENGINEERING_VEHICLE', index=5, number=5,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SV_SCHOOL_BUS', index=6, number=6,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SV_BUS', index=7, number=7,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SV_SPRINKLER', index=8, number=8,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SV_SPRINKLER_WITHOUT_BRUSH', index=9, number=9,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SV_TRUCK_WITH_SLOPE', index=10, number=10,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SV_SANITATION_VEHICLE', index=11, number=11,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=14784,
  serialized_end=15065,
)
_sym_db.RegisterEnumDescriptor(_SPECIALVEHICLETYPE)

SpecialVehicleType = enum_type_wrapper.EnumTypeWrapper(_SPECIALVEHICLETYPE)
_PROFESSION = _descriptor.EnumDescriptor(
  name='Profession',
  full_name='idg.perception.autolabeling.Profession',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='POINTLESS_PROFESSION', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CONSTRUCTOR_OR_CLEANER', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='POLICE', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=15067,
  serialized_end=15145,
)
_sym_db.RegisterEnumDescriptor(_PROFESSION)

Profession = enum_type_wrapper.EnumTypeWrapper(_PROFESSION)
_FACETOWARDSSTATUS = _descriptor.EnumDescriptor(
  name='FaceTowardsStatus',
  full_name='idg.perception.autolabeling.FaceTowardsStatus',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='FTS_UNKNOWN', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FTS_FRONT', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FTS_OTHER', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=15147,
  serialized_end=15213,
)
_sym_db.RegisterEnumDescriptor(_FACETOWARDSSTATUS)

FaceTowardsStatus = enum_type_wrapper.EnumTypeWrapper(_FACETOWARDSSTATUS)
_REVERSINGTYPE = _descriptor.EnumDescriptor(
  name='ReversingType',
  full_name='idg.perception.autolabeling.ReversingType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='RT_UNKNOWN', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='RT_NOT_REVERSING', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='RT_NORMAL_REVERSING', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='RT_SAME_THREE_POINT', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='RT_OPPOSITE_THREE_POINT', index=4, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='RT_PARALLEL_PARKING', index=5, number=5,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='RT_PARALLEL_PARKING_OUT', index=6, number=6,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='RT_IGNORE', index=7, number=7,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=15216,
  serialized_end=15417,
)
_sym_db.RegisterEnumDescriptor(_REVERSINGTYPE)

ReversingType = enum_type_wrapper.EnumTypeWrapper(_REVERSINGTYPE)
_REVERSINGSUBTYPE = _descriptor.EnumDescriptor(
  name='ReversingSubType',
  full_name='idg.perception.autolabeling.ReversingSubType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='RST_UNKNOWN', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='RST_FORWARD', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='RST_STATIC', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='RST_BACKWARD', index=3, number=3,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=15419,
  serialized_end=15505,
)
_sym_db.RegisterEnumDescriptor(_REVERSINGSUBTYPE)

ReversingSubType = enum_type_wrapper.EnumTypeWrapper(_REVERSINGSUBTYPE)
_CROSSINGTYPE = _descriptor.EnumDescriptor(
  name='CrossingType',
  full_name='idg.perception.autolabeling.CrossingType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='CT_UNKNOWN', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CT_NOT_CROSSING', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CT_CROSSING', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CT_IGNORE', index=3, number=3,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=15507,
  serialized_end=15590,
)
_sym_db.RegisterEnumDescriptor(_CROSSINGTYPE)

CrossingType = enum_type_wrapper.EnumTypeWrapper(_CROSSINGTYPE)
_CROSSINGSUBTYPE = _descriptor.EnumDescriptor(
  name='CrossingSubType',
  full_name='idg.perception.autolabeling.CrossingSubType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='CST_UNKNOWN', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CST_NORMAL_CROSSING', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CST_SIDE_TURN_CROSSING', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CST_WAITING_CROSSING', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CST_PARALLEL_NOT_CROSSING', index=4, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CST_DETOUR_NOT_CROSSING', index=5, number=5,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CST_STATIC_NOT_CROSSING', index=6, number=6,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CST_OTHER_NOT_CROSSING', index=7, number=7,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=15593,
  serialized_end=15823,
)
_sym_db.RegisterEnumDescriptor(_CROSSINGSUBTYPE)

CrossingSubType = enum_type_wrapper.EnumTypeWrapper(_CROSSINGSUBTYPE)
_TURNTYPE = _descriptor.EnumDescriptor(
  name='TurnType',
  full_name='idg.perception.autolabeling.TurnType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='TT_UNKNOWN', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TT_STRAIGHT', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TT_TURNING', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=15825,
  serialized_end=15884,
)
_sym_db.RegisterEnumDescriptor(_TURNTYPE)

TurnType = enum_type_wrapper.EnumTypeWrapper(_TURNTYPE)
_ABROADGRIDHEIGHTLEVEL = _descriptor.EnumDescriptor(
  name='AbRoadGridHeightLevel',
  full_name='idg.perception.autolabeling.AbRoadGridHeightLevel',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='LEVEL_UNKNOWN', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='LEVEL_FLAT', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='LEVEL_UP_1', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='LEVEL_UP_2', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='LEVEL_UP_3', index=4, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='LEVEL_DOWN_1', index=5, number=5,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='LEVEL_DOWN_2', index=6, number=6,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='LEVEL_DOWN_3', index=7, number=7,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=15887,
  serialized_end=16047,
)
_sym_db.RegisterEnumDescriptor(_ABROADGRIDHEIGHTLEVEL)

AbRoadGridHeightLevel = enum_type_wrapper.EnumTypeWrapper(_ABROADGRIDHEIGHTLEVEL)
_ABROADGRIDVIEWSTATE = _descriptor.EnumDescriptor(
  name='AbRoadGridViewState',
  full_name='idg.perception.autolabeling.AbRoadGridViewState',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='STATE_UNKNOWN', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='STATE_OUT_VIEW', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='STATE_IN_VIEW', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=16049,
  serialized_end=16128,
)
_sym_db.RegisterEnumDescriptor(_ABROADGRIDVIEWSTATE)

AbRoadGridViewState = enum_type_wrapper.EnumTypeWrapper(_ABROADGRIDVIEWSTATE)
_QCFAILTYPE = _descriptor.EnumDescriptor(
  name='QcFailType',
  full_name='idg.perception.autolabeling.QcFailType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='QcFailType_UNKNOWN', index=0, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QcFailType_FG_ONLINE_MODEL_FN', index=1, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QcFailType_FG_ONLINE_MODEL_FP', index=2, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QcFailType_FG_BIG_MODEL_FN', index=3, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QcFailType_FG_BIG_MODEL_FP', index=4, number=5,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QcFailType_FG_ONLINE_MODEL_KEY_FN', index=5, number=6,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QcFailType_BG_NOISE_FN', index=6, number=7,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QcFailType_BG_NOISE_FP', index=7, number=8,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=16131,
  serialized_end=16396,
)
_sym_db.RegisterEnumDescriptor(_QCFAILTYPE)

QcFailType = enum_type_wrapper.EnumTypeWrapper(_QCFAILTYPE)
ROIStateInfo_UNKNOWN = 0
ROIStateInfo_IN_ROI = 1
ROIStateInfo_OUT_ROI = 2
MotionStateInfo_UNKNOWN = 0
MotionStateInfo_MOVING = 1
MotionStateInfo_STATIONARY = 2
MotionStateInfo_UNCERTAIN = 3
FullLabel = 0
WeakLabel = 1
LoseLidar = 0
LoseImage = 1
LoseParam = 2
DetectionQCInfo_UNKNOWN = 0
DetectionQCInfo_FN = 1
DetectionQCInfo_FP = 2
DetectionQCInfo_TP = 3
OcclusionState_No = 0
OcclusionState_Part = 1
OcclusionState_Complete = 2
OcclusionState_Max = 3
TargetType_UNKNOWN = 0
TargetType_BigMot = 1
TargetType_VRU = 2
TargetType_TrafficLight = 3
TargetType_LowObs = 4
TargetType_Barrier = 5
TargetType_SmallMot = 6
SV_NONE = 0
SV_POLICE_VEHICLE = 1
SV_POLICE_MOTORCYCLE = 2
SV_FIRE_ENGINE = 3
SV_AMBULANCE = 4
SV_ENGINEERING_VEHICLE = 5
SV_SCHOOL_BUS = 6
SV_BUS = 7
SV_SPRINKLER = 8
SV_SPRINKLER_WITHOUT_BRUSH = 9
SV_TRUCK_WITH_SLOPE = 10
SV_SANITATION_VEHICLE = 11
POINTLESS_PROFESSION = 0
CONSTRUCTOR_OR_CLEANER = 1
POLICE = 2
FTS_UNKNOWN = 0
FTS_FRONT = 1
FTS_OTHER = 2
RT_UNKNOWN = 0
RT_NOT_REVERSING = 1
RT_NORMAL_REVERSING = 2
RT_SAME_THREE_POINT = 3
RT_OPPOSITE_THREE_POINT = 4
RT_PARALLEL_PARKING = 5
RT_PARALLEL_PARKING_OUT = 6
RT_IGNORE = 7
RST_UNKNOWN = 0
RST_FORWARD = 1
RST_STATIC = 2
RST_BACKWARD = 3
CT_UNKNOWN = 0
CT_NOT_CROSSING = 1
CT_CROSSING = 2
CT_IGNORE = 3
CST_UNKNOWN = 0
CST_NORMAL_CROSSING = 1
CST_SIDE_TURN_CROSSING = 2
CST_WAITING_CROSSING = 3
CST_PARALLEL_NOT_CROSSING = 4
CST_DETOUR_NOT_CROSSING = 5
CST_STATIC_NOT_CROSSING = 6
CST_OTHER_NOT_CROSSING = 7
TT_UNKNOWN = 0
TT_STRAIGHT = 1
TT_TURNING = 2
LEVEL_UNKNOWN = 0
LEVEL_FLAT = 1
LEVEL_UP_1 = 2
LEVEL_UP_2 = 3
LEVEL_UP_3 = 4
LEVEL_DOWN_1 = 5
LEVEL_DOWN_2 = 6
LEVEL_DOWN_3 = 7
STATE_UNKNOWN = 0
STATE_OUT_VIEW = 1
STATE_IN_VIEW = 2
QcFailType_UNKNOWN = 1
QcFailType_FG_ONLINE_MODEL_FN = 2
QcFailType_FG_ONLINE_MODEL_FP = 3
QcFailType_FG_BIG_MODEL_FN = 4
QcFailType_FG_BIG_MODEL_FP = 5
QcFailType_FG_ONLINE_MODEL_KEY_FN = 6
QcFailType_BG_NOISE_FN = 7
QcFailType_BG_NOISE_FP = 8


_BIGMOTOBJECTINFO_FITSTATUS = _descriptor.EnumDescriptor(
  name='FitStatus',
  full_name='idg.perception.autolabeling.BigMotObjectInfo.FitStatus',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='WithRearViewMirror', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='WithOutRearViewMirror', index=1, number=1,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=3427,
  serialized_end=3489,
)
_sym_db.RegisterEnumDescriptor(_BIGMOTOBJECTINFO_FITSTATUS)

_MANUALLABELOBJECTINFO_CHECKSTATUS = _descriptor.EnumDescriptor(
  name='CheckStatus',
  full_name='idg.perception.autolabeling.ManualLabelObjectInfo.CheckStatus',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='ToBeChecked', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Ignored', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Finished', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=4155,
  serialized_end=4212,
)
_sym_db.RegisterEnumDescriptor(_MANUALLABELOBJECTINFO_CHECKSTATUS)

_MANUALQUALITYINSPECTIONINFO_CHECKSTATUS = _descriptor.EnumDescriptor(
  name='CheckStatus',
  full_name='idg.perception.autolabeling.ManualQualityInspectionInfo.CheckStatus',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='ToBeChecked', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Ignored', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Finished', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=4155,
  serialized_end=4212,
)
_sym_db.RegisterEnumDescriptor(_MANUALQUALITYINSPECTIONINFO_CHECKSTATUS)

_MANUALQUALITYINSPECTIONINFO_ERRORTYPE = _descriptor.EnumDescriptor(
  name='ErrorType',
  full_name='idg.perception.autolabeling.ManualQualityInspectionInfo.ErrorType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='ErrorType_Missing', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ErrorType_Error', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ErrorType_Redundant', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=4677,
  serialized_end=4757,
)
_sym_db.RegisterEnumDescriptor(_MANUALQUALITYINSPECTIONINFO_ERRORTYPE)

_LIGHTSIGNAL_STATUS = _descriptor.EnumDescriptor(
  name='Status',
  full_name='idg.perception.autolabeling.LightSignal.Status',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='UNKNOWN', index=0, number=-1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OFF', index=1, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ON', index=2, number=1,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=5091,
  serialized_end=5138,
)
_sym_db.RegisterEnumDescriptor(_LIGHTSIGNAL_STATUS)


_VECTORF = _descriptor.Descriptor(
  name='VectorF',
  full_name='idg.perception.autolabeling.VectorF',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='data', full_name='idg.perception.autolabeling.VectorF.data', index=0,
      number=1, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=58,
  serialized_end=81,
)


_VECTORD = _descriptor.Descriptor(
  name='VectorD',
  full_name='idg.perception.autolabeling.VectorD',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='data', full_name='idg.perception.autolabeling.VectorD.data', index=0,
      number=1, type=1, cpp_type=5, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=83,
  serialized_end=106,
)


_IMGSIZEINFO = _descriptor.Descriptor(
  name='ImgsizeInfo',
  full_name='idg.perception.autolabeling.ImgsizeInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='height', full_name='idg.perception.autolabeling.ImgsizeInfo.height', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=True, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='width', full_name='idg.perception.autolabeling.ImgsizeInfo.width', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=True, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=108,
  serialized_end=158,
)


_POINTF = _descriptor.Descriptor(
  name='PointF',
  full_name='idg.perception.autolabeling.PointF',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='idg.perception.autolabeling.PointF.x', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='y', full_name='idg.perception.autolabeling.PointF.y', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='z', full_name='idg.perception.autolabeling.PointF.z', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='intensity', full_name='idg.perception.autolabeling.PointF.intensity', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=160,
  serialized_end=232,
)


_POINTD = _descriptor.Descriptor(
  name='PointD',
  full_name='idg.perception.autolabeling.PointD',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='idg.perception.autolabeling.PointD.x', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=True, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='y', full_name='idg.perception.autolabeling.PointD.y', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=True, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='z', full_name='idg.perception.autolabeling.PointD.z', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=True, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='intensity', full_name='idg.perception.autolabeling.PointD.intensity', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=True, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=234,
  serialized_end=306,
)


_POINTFCLOUDINFO = _descriptor.Descriptor(
  name='PointFCloudInfo',
  full_name='idg.perception.autolabeling.PointFCloudInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='points', full_name='idg.perception.autolabeling.PointFCloudInfo.points', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='width', full_name='idg.perception.autolabeling.PointFCloudInfo.width', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=True, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='height', full_name='idg.perception.autolabeling.PointFCloudInfo.height', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=True, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='points_height', full_name='idg.perception.autolabeling.PointFCloudInfo.points_height', index=3,
      number=4, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='points_label', full_name='idg.perception.autolabeling.PointFCloudInfo.points_label', index=4,
      number=5, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\020\001'), file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='points_index', full_name='idg.perception.autolabeling.PointFCloudInfo.points_index', index=5,
      number=6, type=5, cpp_type=1, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\020\001'), file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=309,
  serialized_end=491,
)


_TRANSLATION = _descriptor.Descriptor(
  name='Translation',
  full_name='idg.perception.autolabeling.Translation',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='idg.perception.autolabeling.Translation.x', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=True, default_value=(1e10000 * 0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='y', full_name='idg.perception.autolabeling.Translation.y', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=True, default_value=(1e10000 * 0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='z', full_name='idg.perception.autolabeling.Translation.z', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=True, default_value=(1e10000 * 0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=493,
  serialized_end=554,
)


_QUATERNION = _descriptor.Descriptor(
  name='Quaternion',
  full_name='idg.perception.autolabeling.Quaternion',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='qx', full_name='idg.perception.autolabeling.Quaternion.qx', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=True, default_value=(1e10000 * 0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qy', full_name='idg.perception.autolabeling.Quaternion.qy', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=True, default_value=(1e10000 * 0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qz', full_name='idg.perception.autolabeling.Quaternion.qz', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=True, default_value=(1e10000 * 0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qw', full_name='idg.perception.autolabeling.Quaternion.qw', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=True, default_value=(1e10000 * 0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=556,
  serialized_end=636,
)


_POSEINFO = _descriptor.Descriptor(
  name='PoseInfo',
  full_name='idg.perception.autolabeling.PoseInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='translation', full_name='idg.perception.autolabeling.PoseInfo.translation', index=0,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quaternion', full_name='idg.perception.autolabeling.PoseInfo.quaternion', index=1,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=639,
  serialized_end=773,
)


_POLYGONDTYPE = _descriptor.Descriptor(
  name='PolygonDType',
  full_name='idg.perception.autolabeling.PolygonDType',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='points', full_name='idg.perception.autolabeling.PolygonDType.points', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='width', full_name='idg.perception.autolabeling.PolygonDType.width', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=True, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='height', full_name='idg.perception.autolabeling.PolygonDType.height', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=True, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=775,
  serialized_end=879,
)


_LIDARCAMERAINFO = _descriptor.Descriptor(
  name='LidarCameraInfo',
  full_name='idg.perception.autolabeling.LidarCameraInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='sensor_name', full_name='idg.perception.autolabeling.LidarCameraInfo.sensor_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=True, default_value=_b("UNKNOWN").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='match_distance', full_name='idg.perception.autolabeling.LidarCameraInfo.match_distance', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=True, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='project_area', full_name='idg.perception.autolabeling.LidarCameraInfo.project_area', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=True, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='increase_area', full_name='idg.perception.autolabeling.LidarCameraInfo.increase_area', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=True, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pts_inner_ratio', full_name='idg.perception.autolabeling.LidarCameraInfo.pts_inner_ratio', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=True, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='visible', full_name='idg.perception.autolabeling.LidarCameraInfo.visible', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=True, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='proj_box', full_name='idg.perception.autolabeling.LidarCameraInfo.proj_box', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='image_box', full_name='idg.perception.autolabeling.LidarCameraInfo.image_box', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='image_confidence', full_name='idg.perception.autolabeling.LidarCameraInfo.image_confidence', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=True, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='idg.perception.autolabeling.LidarCameraInfo.type', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=True, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='idg.perception.autolabeling.LidarCameraInfo.sub_type', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=True, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='truncate', full_name='idg.perception.autolabeling.LidarCameraInfo.truncate', index=11,
      number=12, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(1),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='occ_ratio', full_name='idg.perception.autolabeling.LidarCameraInfo.occ_ratio', index=12,
      number=13, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(1),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='uid_2d', full_name='idg.perception.autolabeling.LidarCameraInfo.uid_2d', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=True, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=882,
  serialized_end=1298,
)


_DATACOMPLETENESS = _descriptor.Descriptor(
  name='DataCompleteness',
  full_name='idg.perception.autolabeling.DataCompleteness',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='complete', full_name='idg.perception.autolabeling.DataCompleteness.complete', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=True, default_value=True,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lose_data_info', full_name='idg.perception.autolabeling.DataCompleteness.lose_data_info', index=1,
      number=2, type=14, cpp_type=8, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1300,
  serialized_end=1409,
)


_METRICSUPPLEMENTINFO = _descriptor.Descriptor(
  name='MetricSupplementInfo',
  full_name='idg.perception.autolabeling.MetricSupplementInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='box_iou', full_name='idg.perception.autolabeling.MetricSupplementInfo.box_iou', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(-1),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='points_iou', full_name='idg.perception.autolabeling.MetricSupplementInfo.points_iou', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(-1),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distance_error', full_name='idg.perception.autolabeling.MetricSupplementInfo.distance_error', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(-1),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='box_ratio', full_name='idg.perception.autolabeling.MetricSupplementInfo.box_ratio', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(-1),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='map_box', full_name='idg.perception.autolabeling.MetricSupplementInfo.map_box', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1412,
  serialized_end=1585,
)


_FUSIONSUPPLEMENT = _descriptor.Descriptor(
  name='FusionSupplement',
  full_name='idg.perception.autolabeling.FusionSupplement',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='confidence', full_name='idg.perception.autolabeling.FusionSupplement.confidence', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(-1),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='conf_seq_min', full_name='idg.perception.autolabeling.FusionSupplement.conf_seq_min', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(-1),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='conf_seq_mid', full_name='idg.perception.autolabeling.FusionSupplement.conf_seq_mid', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(-1),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='conf_seq_max', full_name='idg.perception.autolabeling.FusionSupplement.conf_seq_max', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(-1),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='conf_lidar_det', full_name='idg.perception.autolabeling.FusionSupplement.conf_lidar_det', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(-1),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='conf_lidar_parse', full_name='idg.perception.autolabeling.FusionSupplement.conf_lidar_parse', index=5,
      number=6, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(-1),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='conf_camera_det', full_name='idg.perception.autolabeling.FusionSupplement.conf_camera_det', index=6,
      number=7, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(-1),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='conf_camera_parse', full_name='idg.perception.autolabeling.FusionSupplement.conf_camera_parse', index=7,
      number=8, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(-1),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='num_foreground_pts', full_name='idg.perception.autolabeling.FusionSupplement.num_foreground_pts', index=8,
      number=9, type=13, cpp_type=3, label=1,
      has_default_value=True, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='num_pts_wo_ground', full_name='idg.perception.autolabeling.FusionSupplement.num_pts_wo_ground', index=9,
      number=10, type=13, cpp_type=3, label=1,
      has_default_value=True, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='conf_seq_window_min', full_name='idg.perception.autolabeling.FusionSupplement.conf_seq_window_min', index=10,
      number=11, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(-1),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='conf_seq_window_mean', full_name='idg.perception.autolabeling.FusionSupplement.conf_seq_window_mean', index=11,
      number=12, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(-1),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='conf_seq_window_max', full_name='idg.perception.autolabeling.FusionSupplement.conf_seq_window_max', index=12,
      number=13, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(-1),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tracking_length', full_name='idg.perception.autolabeling.FusionSupplement.tracking_length', index=13,
      number=14, type=13, cpp_type=3, label=1,
      has_default_value=True, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='polygon_area', full_name='idg.perception.autolabeling.FusionSupplement.polygon_area', index=14,
      number=50, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='polygon_pts', full_name='idg.perception.autolabeling.FusionSupplement.polygon_pts', index=15,
      number=51, type=13, cpp_type=3, label=1,
      has_default_value=True, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1588,
  serialized_end=2064,
)


_CAMERASUPPLEMENT = _descriptor.Descriptor(
  name='CameraSupplement',
  full_name='idg.perception.autolabeling.CameraSupplement',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='occlusion_state', full_name='idg.perception.autolabeling.CameraSupplement.occlusion_state', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_onroad', full_name='idg.perception.autolabeling.CameraSupplement.is_onroad', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=True, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_ignored', full_name='idg.perception.autolabeling.CameraSupplement.is_ignored', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=True, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='truncation', full_name='idg.perception.autolabeling.CameraSupplement.truncation', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=True, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='alpha', full_name='idg.perception.autolabeling.CameraSupplement.alpha', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(-1),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2067,
  serialized_end=2250,
)


_LIDARSUPPLEMENT = _descriptor.Descriptor(
  name='LidarSupplement',
  full_name='idg.perception.autolabeling.LidarSupplement',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='is_on_fence_area', full_name='idg.perception.autolabeling.LidarSupplement.is_on_fence_area', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=True, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_on_main_lanes', full_name='idg.perception.autolabeling.LidarSupplement.is_on_main_lanes', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=True, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_swerve_bigmot', full_name='idg.perception.autolabeling.LidarSupplement.is_swerve_bigmot', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=True, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lidar_camera_info', full_name='idg.perception.autolabeling.LidarSupplement.lidar_camera_info', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='local_cloud', full_name='idg.perception.autolabeling.LidarSupplement.local_cloud', index=4,
      number=20, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2253,
  serialized_end=2509,
)


_QUALITYCHECKOBJECTINFO = _descriptor.Descriptor(
  name='QualityCheckObjectInfo',
  full_name='idg.perception.autolabeling.QualityCheckObjectInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='motion_state_nq', full_name='idg.perception.autolabeling.QualityCheckObjectInfo.motion_state_nq', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=True, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='velocity_nq', full_name='idg.perception.autolabeling.QualityCheckObjectInfo.velocity_nq', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=True, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='detection_qc_info', full_name='idg.perception.autolabeling.QualityCheckObjectInfo.detection_qc_info', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='asso_2d_3d_nq', full_name='idg.perception.autolabeling.QualityCheckObjectInfo.asso_2d_3d_nq', index=3,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=True, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='asso_2d_3d_update', full_name='idg.perception.autolabeling.QualityCheckObjectInfo.asso_2d_3d_update', index=4,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=True, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='need_high_light', full_name='idg.perception.autolabeling.QualityCheckObjectInfo.need_high_light', index=5,
      number=30, type=8, cpp_type=7, label=1,
      has_default_value=True, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2512,
  serialized_end=2765,
)


_QUALITYCHECKFRAMEINFO = _descriptor.Descriptor(
  name='QualityCheckFrameInfo',
  full_name='idg.perception.autolabeling.QualityCheckFrameInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='motion_state_nq', full_name='idg.perception.autolabeling.QualityCheckFrameInfo.motion_state_nq', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=True, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='velocity_nq', full_name='idg.perception.autolabeling.QualityCheckFrameInfo.velocity_nq', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=True, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='detection_nq', full_name='idg.perception.autolabeling.QualityCheckFrameInfo.detection_nq', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=True, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='asso_2d_3d_nq', full_name='idg.perception.autolabeling.QualityCheckFrameInfo.asso_2d_3d_nq', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=True, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2768,
  serialized_end=2910,
)


_TRAFFICLIGHTSUPPLEMENTINFO = _descriptor.Descriptor(
  name='TrafficLightSupplementInfo',
  full_name='idg.perception.autolabeling.TrafficLightSupplementInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='map_uid', full_name='idg.perception.autolabeling.TrafficLightSupplementInfo.map_uid', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=True, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='angle', full_name='idg.perception.autolabeling.TrafficLightSupplementInfo.angle', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(-180),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type_shape', full_name='idg.perception.autolabeling.TrafficLightSupplementInfo.type_shape', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=True, default_value=-1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='occluded_degree', full_name='idg.perception.autolabeling.TrafficLightSupplementInfo.occluded_degree', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(-1),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lanppost_id', full_name='idg.perception.autolabeling.TrafficLightSupplementInfo.lanppost_id', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(-1),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='manual_direction', full_name='idg.perception.autolabeling.TrafficLightSupplementInfo.manual_direction', index=5,
      number=6, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(-1),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='occluded_direction', full_name='idg.perception.autolabeling.TrafficLightSupplementInfo.occluded_direction', index=6,
      number=7, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(-1),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_ped', full_name='idg.perception.autolabeling.TrafficLightSupplementInfo.is_ped', index=7,
      number=8, type=5, cpp_type=1, label=1,
      has_default_value=True, default_value=-1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2913,
  serialized_end=3141,
)


_BIGMOTOBJECTINFO = _descriptor.Descriptor(
  name='BigMotObjectInfo',
  full_name='idg.perception.autolabeling.BigMotObjectInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='fit_status', full_name='idg.perception.autolabeling.BigMotObjectInfo.fit_status', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=True, default_value=1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='center', full_name='idg.perception.autolabeling.BigMotObjectInfo.center', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='size', full_name='idg.perception.autolabeling.BigMotObjectInfo.size', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='direction', full_name='idg.perception.autolabeling.BigMotObjectInfo.direction', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _BIGMOTOBJECTINFO_FITSTATUS,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3144,
  serialized_end=3489,
)


_VISIBLEINFO = _descriptor.Descriptor(
  name='VisibleInfo',
  full_name='idg.perception.autolabeling.VisibleInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='visible_ratio', full_name='idg.perception.autolabeling.VisibleInfo.visible_ratio', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3491,
  serialized_end=3530,
)


_PRELABELINFO = _descriptor.Descriptor(
  name='PreLabelInfo',
  full_name='idg.perception.autolabeling.PreLabelInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='shape_confidence', full_name='idg.perception.autolabeling.PreLabelInfo.shape_confidence', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(-1),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='label_free', full_name='idg.perception.autolabeling.PreLabelInfo.label_free', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=True, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='big_mot_obj_info', full_name='idg.perception.autolabeling.PreLabelInfo.big_mot_obj_info', index=2,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='visible_info', full_name='idg.perception.autolabeling.PreLabelInfo.visible_info', index=3,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3533,
  serialized_end=3741,
)


_MANUALLABELOBJECTINFO = _descriptor.Descriptor(
  name='ManualLabelObjectInfo',
  full_name='idg.perception.autolabeling.ManualLabelObjectInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='check_state', full_name='idg.perception.autolabeling.ManualLabelObjectInfo.check_state', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=True, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='label_state', full_name='idg.perception.autolabeling.ManualLabelObjectInfo.label_state', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=True, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='num_geometry_op', full_name='idg.perception.autolabeling.ManualLabelObjectInfo.num_geometry_op', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=True, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='num_property_op', full_name='idg.perception.autolabeling.ManualLabelObjectInfo.num_property_op', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=True, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quality_state', full_name='idg.perception.autolabeling.ManualLabelObjectInfo.quality_state', index=4,
      number=5, type=14, cpp_type=8, label=1,
      has_default_value=True, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='meta_manual_label_info', full_name='idg.perception.autolabeling.ManualLabelObjectInfo.meta_manual_label_info', index=5,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=True, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _MANUALLABELOBJECTINFO_CHECKSTATUS,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3744,
  serialized_end=4212,
)


_MANUALLABELFRAMEINFO = _descriptor.Descriptor(
  name='ManualLabelFrameInfo',
  full_name='idg.perception.autolabeling.ManualLabelFrameInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='checked', full_name='idg.perception.autolabeling.ManualLabelFrameInfo.checked', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=True, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4214,
  serialized_end=4260,
)


_MANUALQUALITYINSPECTIONINFO = _descriptor.Descriptor(
  name='ManualQualityInspectionInfo',
  full_name='idg.perception.autolabeling.ManualQualityInspectionInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='idg.perception.autolabeling.ManualQualityInspectionInfo.id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=True, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='check_state', full_name='idg.perception.autolabeling.ManualQualityInspectionInfo.check_state', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=True, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='error_type', full_name='idg.perception.autolabeling.ManualQualityInspectionInfo.error_type', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=True, default_value=1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='error_info', full_name='idg.perception.autolabeling.ManualQualityInspectionInfo.error_info', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=True, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extra_info', full_name='idg.perception.autolabeling.ManualQualityInspectionInfo.extra_info', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=True, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='location', full_name='idg.perception.autolabeling.ManualQualityInspectionInfo.location', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _MANUALQUALITYINSPECTIONINFO_CHECKSTATUS,
    _MANUALQUALITYINSPECTIONINFO_ERRORTYPE,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4263,
  serialized_end=4757,
)


_DOORSTATUS = _descriptor.Descriptor(
  name='DoorStatus',
  full_name='idg.perception.autolabeling.DoorStatus',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='door_open', full_name='idg.perception.autolabeling.DoorStatus.door_open', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=True, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='left_door_open', full_name='idg.perception.autolabeling.DoorStatus.left_door_open', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=True, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='right_door_open', full_name='idg.perception.autolabeling.DoorStatus.right_door_open', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=True, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='front_door_open', full_name='idg.perception.autolabeling.DoorStatus.front_door_open', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=True, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='back_door_open', full_name='idg.perception.autolabeling.DoorStatus.back_door_open', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=True, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4760,
  serialized_end=4924,
)


_LIGHTSIGNAL = _descriptor.Descriptor(
  name='LightSignal',
  full_name='idg.perception.autolabeling.LightSignal',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='status', full_name='idg.perception.autolabeling.LightSignal.status', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=True, default_value=-1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confidence', full_name='idg.perception.autolabeling.LightSignal.confidence', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='visible_prob', full_name='idg.perception.autolabeling.LightSignal.visible_prob', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='switch_on_prob', full_name='idg.perception.autolabeling.LightSignal.switch_on_prob', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _LIGHTSIGNAL_STATUS,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4927,
  serialized_end=5138,
)


_REVERSINGTIMES = _descriptor.Descriptor(
  name='ReversingTimes',
  full_name='idg.perception.autolabeling.ReversingTimes',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='case_start_time', full_name='idg.perception.autolabeling.ReversingTimes.case_start_time', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=True, default_value=(1e10000 * 0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='case_end_time', full_name='idg.perception.autolabeling.ReversingTimes.case_end_time', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=True, default_value=(1e10000 * 0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reversing_start_time', full_name='idg.perception.autolabeling.ReversingTimes.reversing_start_time', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=True, default_value=(1e10000 * 0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reversing_static_time', full_name='idg.perception.autolabeling.ReversingTimes.reversing_static_time', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=True, default_value=(1e10000 * 0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reversing_backward_time', full_name='idg.perception.autolabeling.ReversingTimes.reversing_backward_time', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=True, default_value=(1e10000 * 0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reversing_end_time', full_name='idg.perception.autolabeling.ReversingTimes.reversing_end_time', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=True, default_value=(1e10000 * 0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5141,
  serialized_end=5357,
)


_REVERSINGSTATUS = _descriptor.Descriptor(
  name='ReversingStatus',
  full_name='idg.perception.autolabeling.ReversingStatus',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='idg.perception.autolabeling.ReversingStatus.type', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=True, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='idg.perception.autolabeling.ReversingStatus.sub_type', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=True, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='times', full_name='idg.perception.autolabeling.ReversingStatus.times', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confidence', full_name='idg.perception.autolabeling.ReversingStatus.confidence', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5360,
  serialized_end=5605,
)


_CROSSINGSTATUS = _descriptor.Descriptor(
  name='CrossingStatus',
  full_name='idg.perception.autolabeling.CrossingStatus',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='idg.perception.autolabeling.CrossingStatus.type', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=True, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='idg.perception.autolabeling.CrossingStatus.sub_type', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=True, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5608,
  serialized_end=5770,
)


_OBJECTINFO = _descriptor.Descriptor(
  name='ObjectInfo',
  full_name='idg.perception.autolabeling.ObjectInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='sensor_name', full_name='idg.perception.autolabeling.ObjectInfo.sensor_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=True, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='idg.perception.autolabeling.ObjectInfo.id', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=True, default_value=-1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='uid', full_name='idg.perception.autolabeling.ObjectInfo.uid', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=True, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='idg.perception.autolabeling.ObjectInfo.type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=True, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='idg.perception.autolabeling.ObjectInfo.sub_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=True, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confidence', full_name='idg.perception.autolabeling.ObjectInfo.confidence', index=5,
      number=6, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(-1),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='box', full_name='idg.perception.autolabeling.ObjectInfo.box', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='center', full_name='idg.perception.autolabeling.ObjectInfo.center', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='size', full_name='idg.perception.autolabeling.ObjectInfo.size', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='direction', full_name='idg.perception.autolabeling.ObjectInfo.direction', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='velocity', full_name='idg.perception.autolabeling.ObjectInfo.velocity', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='acceleration', full_name='idg.perception.autolabeling.ObjectInfo.acceleration', index=11,
      number=41, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='yaw_rate', full_name='idg.perception.autolabeling.ObjectInfo.yaw_rate', index=12,
      number=42, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='front_wheel_steer_angle', full_name='idg.perception.autolabeling.ObjectInfo.front_wheel_steer_angle', index=13,
      number=43, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='vel_dir_diff_angle', full_name='idg.perception.autolabeling.ObjectInfo.vel_dir_diff_angle', index=14,
      number=44, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='turn_type', full_name='idg.perception.autolabeling.ObjectInfo.turn_type', index=15,
      number=45, type=14, cpp_type=8, label=1,
      has_default_value=True, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='origin_center', full_name='idg.perception.autolabeling.ObjectInfo.origin_center', index=16,
      number=140, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='origin_size', full_name='idg.perception.autolabeling.ObjectInfo.origin_size', index=17,
      number=141, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='origin_direction', full_name='idg.perception.autolabeling.ObjectInfo.origin_direction', index=18,
      number=142, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='origin_type', full_name='idg.perception.autolabeling.ObjectInfo.origin_type', index=19,
      number=143, type=9, cpp_type=9, label=1,
      has_default_value=True, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='origin_sub_type', full_name='idg.perception.autolabeling.ObjectInfo.origin_sub_type', index=20,
      number=144, type=9, cpp_type=9, label=1,
      has_default_value=True, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='roi_state', full_name='idg.perception.autolabeling.ObjectInfo.roi_state', index=21,
      number=12, type=14, cpp_type=8, label=1,
      has_default_value=True, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_roi_state', full_name='idg.perception.autolabeling.ObjectInfo.user_roi_state', index=22,
      number=121, type=14, cpp_type=8, label=1,
      has_default_value=True, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='motion_state', full_name='idg.perception.autolabeling.ObjectInfo.motion_state', index=23,
      number=13, type=14, cpp_type=8, label=1,
      has_default_value=True, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='velocity_confidence', full_name='idg.perception.autolabeling.ObjectInfo.velocity_confidence', index=24,
      number=14, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(-1),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='crispness_score', full_name='idg.perception.autolabeling.ObjectInfo.crispness_score', index=25,
      number=15, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(-1),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pts_count_inside', full_name='idg.perception.autolabeling.ObjectInfo.pts_count_inside', index=26,
      number=16, type=5, cpp_type=1, label=1,
      has_default_value=True, default_value=-1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='track_id', full_name='idg.perception.autolabeling.ObjectInfo.track_id', index=27,
      number=17, type=5, cpp_type=1, label=1,
      has_default_value=True, default_value=-1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='track_uid', full_name='idg.perception.autolabeling.ObjectInfo.track_uid', index=28,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=True, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='label_src', full_name='idg.perception.autolabeling.ObjectInfo.label_src', index=29,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=True, default_value=_b("manual").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='polygon', full_name='idg.perception.autolabeling.ObjectInfo.polygon', index=30,
      number=20, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tight_size', full_name='idg.perception.autolabeling.ObjectInfo.tight_size', index=31,
      number=21, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='metric_supplement', full_name='idg.perception.autolabeling.ObjectInfo.metric_supplement', index=32,
      number=30, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='traffic_light_supplement', full_name='idg.perception.autolabeling.ObjectInfo.traffic_light_supplement', index=33,
      number=31, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='metric_debug_supplement', full_name='idg.perception.autolabeling.ObjectInfo.metric_debug_supplement', index=34,
      number=32, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lidar_supplement', full_name='idg.perception.autolabeling.ObjectInfo.lidar_supplement', index=35,
      number=50, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='camera_supplement', full_name='idg.perception.autolabeling.ObjectInfo.camera_supplement', index=36,
      number=51, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fusion_supplement', full_name='idg.perception.autolabeling.ObjectInfo.fusion_supplement', index=37,
      number=52, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sv_type', full_name='idg.perception.autolabeling.ObjectInfo.sv_type', index=38,
      number=53, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pedestrian_profession', full_name='idg.perception.autolabeling.ObjectInfo.pedestrian_profession', index=39,
      number=54, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='door_status', full_name='idg.perception.autolabeling.ObjectInfo.door_status', index=40,
      number=55, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='face_towards_status', full_name='idg.perception.autolabeling.ObjectInfo.face_towards_status', index=41,
      number=56, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='brake_signal', full_name='idg.perception.autolabeling.ObjectInfo.brake_signal', index=42,
      number=57, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='left_turn_signal', full_name='idg.perception.autolabeling.ObjectInfo.left_turn_signal', index=43,
      number=58, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='right_turn_signal', full_name='idg.perception.autolabeling.ObjectInfo.right_turn_signal', index=44,
      number=59, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='emergency_flashers_signal', full_name='idg.perception.autolabeling.ObjectInfo.emergency_flashers_signal', index=45,
      number=60, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='onoff_signal', full_name='idg.perception.autolabeling.ObjectInfo.onoff_signal', index=46,
      number=61, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reversing_signal', full_name='idg.perception.autolabeling.ObjectInfo.reversing_signal', index=47,
      number=62, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='operating_signal', full_name='idg.perception.autolabeling.ObjectInfo.operating_signal', index=48,
      number=63, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pre_label_info', full_name='idg.perception.autolabeling.ObjectInfo.pre_label_info', index=49,
      number=80, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qc_object_info', full_name='idg.perception.autolabeling.ObjectInfo.qc_object_info', index=50,
      number=100, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ml_object_info', full_name='idg.perception.autolabeling.ObjectInfo.ml_object_info', index=51,
      number=120, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='combo_id', full_name='idg.perception.autolabeling.ObjectInfo.combo_id', index=52,
      number=122, type=9, cpp_type=9, label=1,
      has_default_value=True, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='light_head', full_name='idg.perception.autolabeling.ObjectInfo.light_head', index=53,
      number=123, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='velocity_direction', full_name='idg.perception.autolabeling.ObjectInfo.velocity_direction', index=54,
      number=124, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target', full_name='idg.perception.autolabeling.ObjectInfo.target', index=55,
      number=125, type=8, cpp_type=7, label=1,
      has_default_value=True, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='registered', full_name='idg.perception.autolabeling.ObjectInfo.registered', index=56,
      number=126, type=8, cpp_type=7, label=1,
      has_default_value=True, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='registered_pose', full_name='idg.perception.autolabeling.ObjectInfo.registered_pose', index=57,
      number=127, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_type', full_name='idg.perception.autolabeling.ObjectInfo.target_type', index=58,
      number=128, type=14, cpp_type=8, label=1,
      has_default_value=True, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tramcar_is_occlusion', full_name='idg.perception.autolabeling.ObjectInfo.tramcar_is_occlusion', index=59,
      number=129, type=9, cpp_type=9, label=1,
      has_default_value=True, default_value=_b("none").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='blindspot_supplement', full_name='idg.perception.autolabeling.ObjectInfo.blindspot_supplement', index=60,
      number=130, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accessory_is_occlusion', full_name='idg.perception.autolabeling.ObjectInfo.accessory_is_occlusion', index=61,
      number=131, type=9, cpp_type=9, label=1,
      has_default_value=True, default_value=_b("none").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='associated_obstacle_id', full_name='idg.perception.autolabeling.ObjectInfo.associated_obstacle_id', index=62,
      number=132, type=5, cpp_type=1, label=1,
      has_default_value=True, default_value=-1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='semantic_attribute', full_name='idg.perception.autolabeling.ObjectInfo.semantic_attribute', index=63,
      number=133, type=9, cpp_type=9, label=1,
      has_default_value=True, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='occ_ratio', full_name='idg.perception.autolabeling.ObjectInfo.occ_ratio', index=64,
      number=134, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(1),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='velocity_lower_bound', full_name='idg.perception.autolabeling.ObjectInfo.velocity_lower_bound', index=65,
      number=135, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(-1),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='velocity_upper_bound', full_name='idg.perception.autolabeling.ObjectInfo.velocity_upper_bound', index=66,
      number=136, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(-1),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='road_grid_height_level', full_name='idg.perception.autolabeling.ObjectInfo.road_grid_height_level', index=67,
      number=137, type=14, cpp_type=8, label=1,
      has_default_value=True, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='road_grid_view_state', full_name='idg.perception.autolabeling.ObjectInfo.road_grid_view_state', index=68,
      number=138, type=14, cpp_type=8, label=1,
      has_default_value=True, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reversing_status', full_name='idg.perception.autolabeling.ObjectInfo.reversing_status', index=69,
      number=170, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='crossing_status', full_name='idg.perception.autolabeling.ObjectInfo.crossing_status', index=70,
      number=171, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5773,
  serialized_end=9639,
)


_BLINDSPOTPOINTFINFO = _descriptor.Descriptor(
  name='BlindSpotPointFInfo',
  full_name='idg.perception.autolabeling.BlindSpotPointFInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='pixel_x', full_name='idg.perception.autolabeling.BlindSpotPointFInfo.pixel_x', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pixel_y', full_name='idg.perception.autolabeling.BlindSpotPointFInfo.pixel_y', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='point_type', full_name='idg.perception.autolabeling.BlindSpotPointFInfo.point_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='point_score', full_name='idg.perception.autolabeling.BlindSpotPointFInfo.point_score', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9641,
  serialized_end=9737,
)


_BLINDSPOTOBSSUPPLEMENTINFO = _descriptor.Descriptor(
  name='BlindSpotObsSupplementInfo',
  full_name='idg.perception.autolabeling.BlindSpotObsSupplementInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='ground_points', full_name='idg.perception.autolabeling.BlindSpotObsSupplementInfo.ground_points', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9739,
  serialized_end=9840,
)


_RECONSTRUCTEDOBJECTINFO = _descriptor.Descriptor(
  name='ReconstructedObjectInfo',
  full_name='idg.perception.autolabeling.ReconstructedObjectInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='track_id', full_name='idg.perception.autolabeling.ReconstructedObjectInfo.track_id', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=True, default_value=-1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='track_uid', full_name='idg.perception.autolabeling.ReconstructedObjectInfo.track_uid', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=True, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='motion_state', full_name='idg.perception.autolabeling.ReconstructedObjectInfo.motion_state', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=True, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pcd_path', full_name='idg.perception.autolabeling.ReconstructedObjectInfo.pcd_path', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=True, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='base_frame_index', full_name='idg.perception.autolabeling.ReconstructedObjectInfo.base_frame_index', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=True, default_value=-1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='frame_indexs', full_name='idg.perception.autolabeling.ReconstructedObjectInfo.frame_indexs', index=5,
      number=6, type=5, cpp_type=1, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9843,
  serialized_end=10076,
)


_TRAFFICLIGHTHEADINFO = _descriptor.Descriptor(
  name='TrafficLightHeadInfo',
  full_name='idg.perception.autolabeling.TrafficLightHeadInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='idg.perception.autolabeling.TrafficLightHeadInfo.type', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=True, default_value=-1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='box', full_name='idg.perception.autolabeling.TrafficLightHeadInfo.box', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type_conf', full_name='idg.perception.autolabeling.TrafficLightHeadInfo.type_conf', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(-1),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lighthead_color', full_name='idg.perception.autolabeling.TrafficLightHeadInfo.lighthead_color', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=True, default_value=-1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lighthead_color_conf', full_name='idg.perception.autolabeling.TrafficLightHeadInfo.lighthead_color_conf', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(-1),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='uints_digit', full_name='idg.perception.autolabeling.TrafficLightHeadInfo.uints_digit', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=True, default_value=-1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tens_digit', full_name='idg.perception.autolabeling.TrafficLightHeadInfo.tens_digit', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=True, default_value=-1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='light_color', full_name='idg.perception.autolabeling.TrafficLightHeadInfo.light_color', index=7,
      number=8, type=5, cpp_type=1, label=1,
      has_default_value=True, default_value=-1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='label_src', full_name='idg.perception.autolabeling.TrafficLightHeadInfo.label_src', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=True, default_value=_b("manual").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='uid', full_name='idg.perception.autolabeling.TrafficLightHeadInfo.uid', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=True, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10079,
  serialized_end=10372,
)


_HEADER = _descriptor.Descriptor(
  name='Header',
  full_name='idg.perception.autolabeling.Header',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='version', full_name='idg.perception.autolabeling.Header.version', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='date_time', full_name='idg.perception.autolabeling.Header.date_time', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='comments', full_name='idg.perception.autolabeling.Header.comments', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='task_id', full_name='idg.perception.autolabeling.Header.task_id', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='map_version', full_name='idg.perception.autolabeling.Header.map_version', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='online_lidar_model_name', full_name='idg.perception.autolabeling.Header.online_lidar_model_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offline_slam_version', full_name='idg.perception.autolabeling.Header.offline_slam_version', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=True, default_value=_b("None").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offline_calib_version', full_name='idg.perception.autolabeling.Header.offline_calib_version', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=True, default_value=_b("None").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10375,
  serialized_end=10581,
)


_LIDARLABELMERTICINFO = _descriptor.Descriptor(
  name='LidarLabelMerticInfo',
  full_name='idg.perception.autolabeling.LidarLabelMerticInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='manual_contributed_ratio', full_name='idg.perception.autolabeling.LidarLabelMerticInfo.manual_contributed_ratio', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(-1),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='parsing_metric', full_name='idg.perception.autolabeling.LidarLabelMerticInfo.parsing_metric', index=1,
      number=2, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\020\001'), file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10583,
  serialized_end=10671,
)


_PARSELABEL = _descriptor.Descriptor(
  name='ParseLabel',
  full_name='idg.perception.autolabeling.ParseLabel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='cls_parser', full_name='idg.perception.autolabeling.ParseLabel.cls_parser', index=0,
      number=1, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\020\001'), file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cls_sbr', full_name='idg.perception.autolabeling.ParseLabel.cls_sbr', index=1,
      number=2, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\020\001'), file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dx', full_name='idg.perception.autolabeling.ParseLabel.dx', index=2,
      number=3, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\020\001'), file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dy', full_name='idg.perception.autolabeling.ParseLabel.dy', index=3,
      number=4, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\020\001'), file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dz', full_name='idg.perception.autolabeling.ParseLabel.dz', index=4,
      number=5, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\020\001'), file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='in_roi', full_name='idg.perception.autolabeling.ParseLabel.in_roi', index=5,
      number=6, type=8, cpp_type=7, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\020\001'), file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='index', full_name='idg.perception.autolabeling.ParseLabel.index', index=6,
      number=7, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\020\001'), file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confidence_parser', full_name='idg.perception.autolabeling.ParseLabel.confidence_parser', index=7,
      number=8, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\020\001'), file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confidence_sbr', full_name='idg.perception.autolabeling.ParseLabel.confidence_sbr', index=8,
      number=9, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\020\001'), file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10674,
  serialized_end=10877,
)


_LIDARLABELINFO = _descriptor.Descriptor(
  name='LidarLabelInfo',
  full_name='idg.perception.autolabeling.LidarLabelInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='sensor_name', full_name='idg.perception.autolabeling.LidarLabelInfo.sensor_name', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='objects', full_name='idg.perception.autolabeling.LidarLabelInfo.objects', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='label', full_name='idg.perception.autolabeling.LidarLabelInfo.label', index=2,
      number=3, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\020\001'), file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lidar2world_pose', full_name='idg.perception.autolabeling.LidarLabelInfo.lidar2world_pose', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='timestamp_ns', full_name='idg.perception.autolabeling.LidarLabelInfo.timestamp_ns', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='parse_label', full_name='idg.perception.autolabeling.LidarLabelInfo.parse_label', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dense_label', full_name='idg.perception.autolabeling.LidarLabelInfo.dense_label', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lidar2world_pose_offline', full_name='idg.perception.autolabeling.LidarLabelInfo.lidar2world_pose_offline', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lidar2local_pose', full_name='idg.perception.autolabeling.LidarLabelInfo.lidar2local_pose', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='global_world_offset', full_name='idg.perception.autolabeling.LidarLabelInfo.global_world_offset', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='online_location_score', full_name='idg.perception.autolabeling.LidarLabelInfo.online_location_score', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offline_location_score', full_name='idg.perception.autolabeling.LidarLabelInfo.offline_location_score', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='json_key', full_name='idg.perception.autolabeling.LidarLabelInfo.json_key', index=12,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=True, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='metric', full_name='idg.perception.autolabeling.LidarLabelInfo.metric', index=13,
      number=30, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qc_info', full_name='idg.perception.autolabeling.LidarLabelInfo.qc_info', index=14,
      number=100, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ml_info', full_name='idg.perception.autolabeling.LidarLabelInfo.ml_info', index=15,
      number=120, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='object_areas', full_name='idg.perception.autolabeling.LidarLabelInfo.object_areas', index=16,
      number=121, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qi_infos', full_name='idg.perception.autolabeling.LidarLabelInfo.qi_infos', index=17,
      number=122, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10880,
  serialized_end=11840,
)


_CAMERALABELMERTICINFO = _descriptor.Descriptor(
  name='CameraLabelMerticInfo',
  full_name='idg.perception.autolabeling.CameraLabelMerticInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='percent_label', full_name='idg.perception.autolabeling.CameraLabelMerticInfo.percent_label', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(-1),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='percent_map', full_name='idg.perception.autolabeling.CameraLabelMerticInfo.percent_map', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(-1),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='label_lights_num', full_name='idg.perception.autolabeling.CameraLabelMerticInfo.label_lights_num', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(-1),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='map_lights_num', full_name='idg.perception.autolabeling.CameraLabelMerticInfo.map_lights_num', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(-1),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pair_lights_num', full_name='idg.perception.autolabeling.CameraLabelMerticInfo.pair_lights_num', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=True, default_value=float(-1),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11843,
  serialized_end=12005,
)


_CAMERALABELINFO = _descriptor.Descriptor(
  name='CameraLabelInfo',
  full_name='idg.perception.autolabeling.CameraLabelInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='sensor_name', full_name='idg.perception.autolabeling.CameraLabelInfo.sensor_name', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='objects', full_name='idg.perception.autolabeling.CameraLabelInfo.objects', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='traffic_lights', full_name='idg.perception.autolabeling.CameraLabelInfo.traffic_lights', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='label', full_name='idg.perception.autolabeling.CameraLabelInfo.label', index=3,
      number=4, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\020\001'), file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='timestamp_ns', full_name='idg.perception.autolabeling.CameraLabelInfo.timestamp_ns', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='camera2world_pose', full_name='idg.perception.autolabeling.CameraLabelInfo.camera2world_pose', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_undistort', full_name='idg.perception.autolabeling.CameraLabelInfo.is_undistort', index=6,
      number=28, type=8, cpp_type=7, label=1,
      has_default_value=True, default_value=True,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='json_key', full_name='idg.perception.autolabeling.CameraLabelInfo.json_key', index=7,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=True, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='imgsize', full_name='idg.perception.autolabeling.CameraLabelInfo.imgsize', index=8,
      number=30, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='metric', full_name='idg.perception.autolabeling.CameraLabelInfo.metric', index=9,
      number=31, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='blindspot_info', full_name='idg.perception.autolabeling.CameraLabelInfo.blindspot_info', index=10,
      number=40, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qc_info', full_name='idg.perception.autolabeling.CameraLabelInfo.qc_info', index=11,
      number=100, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='img_qc_info', full_name='idg.perception.autolabeling.CameraLabelInfo.img_qc_info', index=12,
      number=150, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12008,
  serialized_end=12660,
)


_BLINDSPOTFRAMEINFO = _descriptor.Descriptor(
  name='BlindSpotFrameInfo',
  full_name='idg.perception.autolabeling.BlindSpotFrameInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='freespace_points', full_name='idg.perception.autolabeling.BlindSpotFrameInfo.freespace_points', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12662,
  serialized_end=12758,
)


_IMGQUALITYINFO = _descriptor.Descriptor(
  name='ImgQualityInfo',
  full_name='idg.perception.autolabeling.ImgQualityInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='num_patches', full_name='idg.perception.autolabeling.ImgQualityInfo.num_patches', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_qc_info', full_name='idg.perception.autolabeling.ImgQualityInfo.region_qc_info', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12760,
  serialized_end=12875,
)


_IMGSUBREGIONQUALITYINFO = _descriptor.Descriptor(
  name='ImgSubRegionQualityInfo',
  full_name='idg.perception.autolabeling.ImgSubRegionQualityInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='patch_index', full_name='idg.perception.autolabeling.ImgSubRegionQualityInfo.patch_index', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dirty_type', full_name='idg.perception.autolabeling.ImgSubRegionQualityInfo.dirty_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quality_score', full_name='idg.perception.autolabeling.ImgSubRegionQualityInfo.quality_score', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12877,
  serialized_end=12966,
)


_AUTOLABELINGLABELINFO = _descriptor.Descriptor(
  name='AutoLabelingLabelInfo',
  full_name='idg.perception.autolabeling.AutoLabelingLabelInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='objects', full_name='idg.perception.autolabeling.AutoLabelingLabelInfo.objects', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lidar_label', full_name='idg.perception.autolabeling.AutoLabelingLabelInfo.lidar_label', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='camera_label', full_name='idg.perception.autolabeling.AutoLabelingLabelInfo.camera_label', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bev_label', full_name='idg.perception.autolabeling.AutoLabelingLabelInfo.bev_label', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12969,
  serialized_end=13248,
)


_FRAMEINFO = _descriptor.Descriptor(
  name='FrameInfo',
  full_name='idg.perception.autolabeling.FrameInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='is_blind_spot_miss', full_name='idg.perception.autolabeling.FrameInfo.is_blind_spot_miss', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=True, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qc_fail', full_name='idg.perception.autolabeling.FrameInfo.qc_fail', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=True, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qc_fail_types', full_name='idg.perception.autolabeling.FrameInfo.qc_fail_types', index=2,
      number=3, type=14, cpp_type=8, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='far_cipv_missing', full_name='idg.perception.autolabeling.FrameInfo.far_cipv_missing', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=True, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13251,
  serialized_end=13418,
)


_LABELINFO = _descriptor.Descriptor(
  name='LabelInfo',
  full_name='idg.perception.autolabeling.LabelInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='idg.perception.autolabeling.LabelInfo.header', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lidar_label', full_name='idg.perception.autolabeling.LabelInfo.lidar_label', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='camera_label', full_name='idg.perception.autolabeling.LabelInfo.camera_label', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='auto_labeling_label', full_name='idg.perception.autolabeling.LabelInfo.auto_labeling_label', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='label_type', full_name='idg.perception.autolabeling.LabelInfo.label_type', index=4,
      number=5, type=14, cpp_type=8, label=1,
      has_default_value=True, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='data_completeness', full_name='idg.perception.autolabeling.LabelInfo.data_completeness', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='frame_info', full_name='idg.perception.autolabeling.LabelInfo.frame_info', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='meta_info', full_name='idg.perception.autolabeling.LabelInfo.meta_info', index=7,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=True, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13421,
  serialized_end=13926,
)


_LABELINFOS = _descriptor.Descriptor(
  name='LabelInfos',
  full_name='idg.perception.autolabeling.LabelInfos',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ns', full_name='idg.perception.autolabeling.LabelInfos.timestamp_ns', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='label_info', full_name='idg.perception.autolabeling.LabelInfos.label_info', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13928,
  serialized_end=14022,
)

_POINTFCLOUDINFO.fields_by_name['points'].message_type = _POINTF
_POSEINFO.fields_by_name['translation'].message_type = _TRANSLATION
_POSEINFO.fields_by_name['quaternion'].message_type = _QUATERNION
_POLYGONDTYPE.fields_by_name['points'].message_type = _POINTD
_LIDARCAMERAINFO.fields_by_name['proj_box'].message_type = _VECTORF
_LIDARCAMERAINFO.fields_by_name['image_box'].message_type = _VECTORF
_DATACOMPLETENESS.fields_by_name['lose_data_info'].enum_type = _LOSEDATAINFO
_METRICSUPPLEMENTINFO.fields_by_name['map_box'].message_type = _VECTORF
_CAMERASUPPLEMENT.fields_by_name['occlusion_state'].enum_type = _OCCLUSIONSTATE
_LIDARSUPPLEMENT.fields_by_name['lidar_camera_info'].message_type = _LIDARCAMERAINFO
_LIDARSUPPLEMENT.fields_by_name['local_cloud'].message_type = _POINTFCLOUDINFO
_QUALITYCHECKOBJECTINFO.fields_by_name['detection_qc_info'].enum_type = _DETECTIONQCINFO
_BIGMOTOBJECTINFO.fields_by_name['fit_status'].enum_type = _BIGMOTOBJECTINFO_FITSTATUS
_BIGMOTOBJECTINFO.fields_by_name['center'].message_type = _VECTORD
_BIGMOTOBJECTINFO.fields_by_name['size'].message_type = _VECTORF
_BIGMOTOBJECTINFO.fields_by_name['direction'].message_type = _VECTORF
_BIGMOTOBJECTINFO_FITSTATUS.containing_type = _BIGMOTOBJECTINFO
_PRELABELINFO.fields_by_name['big_mot_obj_info'].message_type = _BIGMOTOBJECTINFO
_PRELABELINFO.fields_by_name['visible_info'].message_type = _VISIBLEINFO
_MANUALLABELOBJECTINFO.fields_by_name['check_state'].enum_type = _MANUALLABELOBJECTINFO_CHECKSTATUS
_MANUALLABELOBJECTINFO.fields_by_name['label_state'].enum_type = _MANUALLABELOBJECTINFO_CHECKSTATUS
_MANUALLABELOBJECTINFO.fields_by_name['quality_state'].enum_type = _MANUALLABELOBJECTINFO_CHECKSTATUS
_MANUALLABELOBJECTINFO_CHECKSTATUS.containing_type = _MANUALLABELOBJECTINFO
_MANUALQUALITYINSPECTIONINFO.fields_by_name['check_state'].enum_type = _MANUALQUALITYINSPECTIONINFO_CHECKSTATUS
_MANUALQUALITYINSPECTIONINFO.fields_by_name['error_type'].enum_type = _MANUALQUALITYINSPECTIONINFO_ERRORTYPE
_MANUALQUALITYINSPECTIONINFO.fields_by_name['location'].message_type = _VECTORD
_MANUALQUALITYINSPECTIONINFO_CHECKSTATUS.containing_type = _MANUALQUALITYINSPECTIONINFO
_MANUALQUALITYINSPECTIONINFO_ERRORTYPE.containing_type = _MANUALQUALITYINSPECTIONINFO
_LIGHTSIGNAL.fields_by_name['status'].enum_type = _LIGHTSIGNAL_STATUS
_LIGHTSIGNAL_STATUS.containing_type = _LIGHTSIGNAL
_REVERSINGSTATUS.fields_by_name['type'].enum_type = _REVERSINGTYPE
_REVERSINGSTATUS.fields_by_name['sub_type'].enum_type = _REVERSINGSUBTYPE
_REVERSINGSTATUS.fields_by_name['times'].message_type = _REVERSINGTIMES
_CROSSINGSTATUS.fields_by_name['type'].enum_type = _CROSSINGTYPE
_CROSSINGSTATUS.fields_by_name['sub_type'].enum_type = _CROSSINGSUBTYPE
_OBJECTINFO.fields_by_name['box'].message_type = _VECTORF
_OBJECTINFO.fields_by_name['center'].message_type = _VECTORD
_OBJECTINFO.fields_by_name['size'].message_type = _VECTORF
_OBJECTINFO.fields_by_name['direction'].message_type = _VECTORF
_OBJECTINFO.fields_by_name['velocity'].message_type = _VECTORF
_OBJECTINFO.fields_by_name['acceleration'].message_type = _VECTORF
_OBJECTINFO.fields_by_name['turn_type'].enum_type = _TURNTYPE
_OBJECTINFO.fields_by_name['origin_center'].message_type = _VECTORD
_OBJECTINFO.fields_by_name['origin_size'].message_type = _VECTORF
_OBJECTINFO.fields_by_name['origin_direction'].message_type = _VECTORF
_OBJECTINFO.fields_by_name['roi_state'].enum_type = _ROISTATEINFO
_OBJECTINFO.fields_by_name['user_roi_state'].enum_type = _ROISTATEINFO
_OBJECTINFO.fields_by_name['motion_state'].enum_type = _MOTIONSTATEINFO
_OBJECTINFO.fields_by_name['polygon'].message_type = _POLYGONDTYPE
_OBJECTINFO.fields_by_name['tight_size'].message_type = _VECTORF
_OBJECTINFO.fields_by_name['metric_supplement'].message_type = _METRICSUPPLEMENTINFO
_OBJECTINFO.fields_by_name['traffic_light_supplement'].message_type = _TRAFFICLIGHTSUPPLEMENTINFO
_OBJECTINFO.fields_by_name['metric_debug_supplement'].message_type = _METRICSUPPLEMENTINFO
_OBJECTINFO.fields_by_name['lidar_supplement'].message_type = _LIDARSUPPLEMENT
_OBJECTINFO.fields_by_name['camera_supplement'].message_type = _CAMERASUPPLEMENT
_OBJECTINFO.fields_by_name['fusion_supplement'].message_type = _FUSIONSUPPLEMENT
_OBJECTINFO.fields_by_name['sv_type'].enum_type = _SPECIALVEHICLETYPE
_OBJECTINFO.fields_by_name['pedestrian_profession'].enum_type = _PROFESSION
_OBJECTINFO.fields_by_name['door_status'].message_type = _DOORSTATUS
_OBJECTINFO.fields_by_name['face_towards_status'].enum_type = _FACETOWARDSSTATUS
_OBJECTINFO.fields_by_name['brake_signal'].message_type = _LIGHTSIGNAL
_OBJECTINFO.fields_by_name['left_turn_signal'].message_type = _LIGHTSIGNAL
_OBJECTINFO.fields_by_name['right_turn_signal'].message_type = _LIGHTSIGNAL
_OBJECTINFO.fields_by_name['emergency_flashers_signal'].message_type = _LIGHTSIGNAL
_OBJECTINFO.fields_by_name['onoff_signal'].message_type = _LIGHTSIGNAL
_OBJECTINFO.fields_by_name['reversing_signal'].message_type = _LIGHTSIGNAL
_OBJECTINFO.fields_by_name['operating_signal'].message_type = _LIGHTSIGNAL
_OBJECTINFO.fields_by_name['pre_label_info'].message_type = _PRELABELINFO
_OBJECTINFO.fields_by_name['qc_object_info'].message_type = _QUALITYCHECKOBJECTINFO
_OBJECTINFO.fields_by_name['ml_object_info'].message_type = _MANUALLABELOBJECTINFO
_OBJECTINFO.fields_by_name['light_head'].message_type = _TRAFFICLIGHTHEADINFO
_OBJECTINFO.fields_by_name['velocity_direction'].message_type = _VECTORF
_OBJECTINFO.fields_by_name['registered_pose'].message_type = _POSEINFO
_OBJECTINFO.fields_by_name['target_type'].enum_type = _TARGETTYPE
_OBJECTINFO.fields_by_name['blindspot_supplement'].message_type = _BLINDSPOTOBSSUPPLEMENTINFO
_OBJECTINFO.fields_by_name['road_grid_height_level'].enum_type = _ABROADGRIDHEIGHTLEVEL
_OBJECTINFO.fields_by_name['road_grid_view_state'].enum_type = _ABROADGRIDVIEWSTATE
_OBJECTINFO.fields_by_name['reversing_status'].message_type = _REVERSINGSTATUS
_OBJECTINFO.fields_by_name['crossing_status'].message_type = _CROSSINGSTATUS
_BLINDSPOTOBSSUPPLEMENTINFO.fields_by_name['ground_points'].message_type = _BLINDSPOTPOINTFINFO
_RECONSTRUCTEDOBJECTINFO.fields_by_name['motion_state'].enum_type = _MOTIONSTATEINFO
_TRAFFICLIGHTHEADINFO.fields_by_name['box'].message_type = _VECTORF
_LIDARLABELINFO.fields_by_name['objects'].message_type = _OBJECTINFO
_LIDARLABELINFO.fields_by_name['lidar2world_pose'].message_type = _POSEINFO
_LIDARLABELINFO.fields_by_name['parse_label'].message_type = _PARSELABEL
_LIDARLABELINFO.fields_by_name['dense_label'].message_type = _PARSELABEL
_LIDARLABELINFO.fields_by_name['lidar2world_pose_offline'].message_type = _POSEINFO
_LIDARLABELINFO.fields_by_name['lidar2local_pose'].message_type = _POSEINFO
_LIDARLABELINFO.fields_by_name['global_world_offset'].message_type = _TRANSLATION
_LIDARLABELINFO.fields_by_name['metric'].message_type = _LIDARLABELMERTICINFO
_LIDARLABELINFO.fields_by_name['qc_info'].message_type = _QUALITYCHECKFRAMEINFO
_LIDARLABELINFO.fields_by_name['ml_info'].message_type = _MANUALLABELFRAMEINFO
_LIDARLABELINFO.fields_by_name['object_areas'].message_type = _OBJECTINFO
_LIDARLABELINFO.fields_by_name['qi_infos'].message_type = _MANUALQUALITYINSPECTIONINFO
_CAMERALABELINFO.fields_by_name['objects'].message_type = _OBJECTINFO
_CAMERALABELINFO.fields_by_name['traffic_lights'].message_type = _OBJECTINFO
_CAMERALABELINFO.fields_by_name['camera2world_pose'].message_type = _POSEINFO
_CAMERALABELINFO.fields_by_name['imgsize'].message_type = _IMGSIZEINFO
_CAMERALABELINFO.fields_by_name['metric'].message_type = _CAMERALABELMERTICINFO
_CAMERALABELINFO.fields_by_name['blindspot_info'].message_type = _BLINDSPOTFRAMEINFO
_CAMERALABELINFO.fields_by_name['qc_info'].message_type = _QUALITYCHECKFRAMEINFO
_CAMERALABELINFO.fields_by_name['img_qc_info'].message_type = _IMGQUALITYINFO
_BLINDSPOTFRAMEINFO.fields_by_name['freespace_points'].message_type = _BLINDSPOTPOINTFINFO
_IMGQUALITYINFO.fields_by_name['region_qc_info'].message_type = _IMGSUBREGIONQUALITYINFO
_AUTOLABELINGLABELINFO.fields_by_name['objects'].message_type = _OBJECTINFO
_AUTOLABELINGLABELINFO.fields_by_name['lidar_label'].message_type = _LIDARLABELINFO
_AUTOLABELINGLABELINFO.fields_by_name['camera_label'].message_type = _CAMERALABELINFO
_AUTOLABELINGLABELINFO.fields_by_name['bev_label'].message_type = _LIDARLABELINFO
_FRAMEINFO.fields_by_name['qc_fail_types'].enum_type = _QCFAILTYPE
_LABELINFO.fields_by_name['header'].message_type = _HEADER
_LABELINFO.fields_by_name['lidar_label'].message_type = _LIDARLABELINFO
_LABELINFO.fields_by_name['camera_label'].message_type = _CAMERALABELINFO
_LABELINFO.fields_by_name['auto_labeling_label'].message_type = _AUTOLABELINGLABELINFO
_LABELINFO.fields_by_name['label_type'].enum_type = _LABELTYPE
_LABELINFO.fields_by_name['data_completeness'].message_type = _DATACOMPLETENESS
_LABELINFO.fields_by_name['frame_info'].message_type = _FRAMEINFO
_LABELINFOS.fields_by_name['label_info'].message_type = _LABELINFO
DESCRIPTOR.message_types_by_name['VectorF'] = _VECTORF
DESCRIPTOR.message_types_by_name['VectorD'] = _VECTORD
DESCRIPTOR.message_types_by_name['ImgsizeInfo'] = _IMGSIZEINFO
DESCRIPTOR.message_types_by_name['PointF'] = _POINTF
DESCRIPTOR.message_types_by_name['PointD'] = _POINTD
DESCRIPTOR.message_types_by_name['PointFCloudInfo'] = _POINTFCLOUDINFO
DESCRIPTOR.message_types_by_name['Translation'] = _TRANSLATION
DESCRIPTOR.message_types_by_name['Quaternion'] = _QUATERNION
DESCRIPTOR.message_types_by_name['PoseInfo'] = _POSEINFO
DESCRIPTOR.message_types_by_name['PolygonDType'] = _POLYGONDTYPE
DESCRIPTOR.message_types_by_name['LidarCameraInfo'] = _LIDARCAMERAINFO
DESCRIPTOR.message_types_by_name['DataCompleteness'] = _DATACOMPLETENESS
DESCRIPTOR.message_types_by_name['MetricSupplementInfo'] = _METRICSUPPLEMENTINFO
DESCRIPTOR.message_types_by_name['FusionSupplement'] = _FUSIONSUPPLEMENT
DESCRIPTOR.message_types_by_name['CameraSupplement'] = _CAMERASUPPLEMENT
DESCRIPTOR.message_types_by_name['LidarSupplement'] = _LIDARSUPPLEMENT
DESCRIPTOR.message_types_by_name['QualityCheckObjectInfo'] = _QUALITYCHECKOBJECTINFO
DESCRIPTOR.message_types_by_name['QualityCheckFrameInfo'] = _QUALITYCHECKFRAMEINFO
DESCRIPTOR.message_types_by_name['TrafficLightSupplementInfo'] = _TRAFFICLIGHTSUPPLEMENTINFO
DESCRIPTOR.message_types_by_name['BigMotObjectInfo'] = _BIGMOTOBJECTINFO
DESCRIPTOR.message_types_by_name['VisibleInfo'] = _VISIBLEINFO
DESCRIPTOR.message_types_by_name['PreLabelInfo'] = _PRELABELINFO
DESCRIPTOR.message_types_by_name['ManualLabelObjectInfo'] = _MANUALLABELOBJECTINFO
DESCRIPTOR.message_types_by_name['ManualLabelFrameInfo'] = _MANUALLABELFRAMEINFO
DESCRIPTOR.message_types_by_name['ManualQualityInspectionInfo'] = _MANUALQUALITYINSPECTIONINFO
DESCRIPTOR.message_types_by_name['DoorStatus'] = _DOORSTATUS
DESCRIPTOR.message_types_by_name['LightSignal'] = _LIGHTSIGNAL
DESCRIPTOR.message_types_by_name['ReversingTimes'] = _REVERSINGTIMES
DESCRIPTOR.message_types_by_name['ReversingStatus'] = _REVERSINGSTATUS
DESCRIPTOR.message_types_by_name['CrossingStatus'] = _CROSSINGSTATUS
DESCRIPTOR.message_types_by_name['ObjectInfo'] = _OBJECTINFO
DESCRIPTOR.message_types_by_name['BlindSpotPointFInfo'] = _BLINDSPOTPOINTFINFO
DESCRIPTOR.message_types_by_name['BlindSpotObsSupplementInfo'] = _BLINDSPOTOBSSUPPLEMENTINFO
DESCRIPTOR.message_types_by_name['ReconstructedObjectInfo'] = _RECONSTRUCTEDOBJECTINFO
DESCRIPTOR.message_types_by_name['TrafficLightHeadInfo'] = _TRAFFICLIGHTHEADINFO
DESCRIPTOR.message_types_by_name['Header'] = _HEADER
DESCRIPTOR.message_types_by_name['LidarLabelMerticInfo'] = _LIDARLABELMERTICINFO
DESCRIPTOR.message_types_by_name['ParseLabel'] = _PARSELABEL
DESCRIPTOR.message_types_by_name['LidarLabelInfo'] = _LIDARLABELINFO
DESCRIPTOR.message_types_by_name['CameraLabelMerticInfo'] = _CAMERALABELMERTICINFO
DESCRIPTOR.message_types_by_name['CameraLabelInfo'] = _CAMERALABELINFO
DESCRIPTOR.message_types_by_name['BlindSpotFrameInfo'] = _BLINDSPOTFRAMEINFO
DESCRIPTOR.message_types_by_name['ImgQualityInfo'] = _IMGQUALITYINFO
DESCRIPTOR.message_types_by_name['ImgSubRegionQualityInfo'] = _IMGSUBREGIONQUALITYINFO
DESCRIPTOR.message_types_by_name['AutoLabelingLabelInfo'] = _AUTOLABELINGLABELINFO
DESCRIPTOR.message_types_by_name['FrameInfo'] = _FRAMEINFO
DESCRIPTOR.message_types_by_name['LabelInfo'] = _LABELINFO
DESCRIPTOR.message_types_by_name['LabelInfos'] = _LABELINFOS
DESCRIPTOR.enum_types_by_name['ROIStateInfo'] = _ROISTATEINFO
DESCRIPTOR.enum_types_by_name['MotionStateInfo'] = _MOTIONSTATEINFO
DESCRIPTOR.enum_types_by_name['LabelType'] = _LABELTYPE
DESCRIPTOR.enum_types_by_name['LoseDataInfo'] = _LOSEDATAINFO
DESCRIPTOR.enum_types_by_name['DetectionQCInfo'] = _DETECTIONQCINFO
DESCRIPTOR.enum_types_by_name['OcclusionState'] = _OCCLUSIONSTATE
DESCRIPTOR.enum_types_by_name['TargetType'] = _TARGETTYPE
DESCRIPTOR.enum_types_by_name['SpecialVehicleType'] = _SPECIALVEHICLETYPE
DESCRIPTOR.enum_types_by_name['Profession'] = _PROFESSION
DESCRIPTOR.enum_types_by_name['FaceTowardsStatus'] = _FACETOWARDSSTATUS
DESCRIPTOR.enum_types_by_name['ReversingType'] = _REVERSINGTYPE
DESCRIPTOR.enum_types_by_name['ReversingSubType'] = _REVERSINGSUBTYPE
DESCRIPTOR.enum_types_by_name['CrossingType'] = _CROSSINGTYPE
DESCRIPTOR.enum_types_by_name['CrossingSubType'] = _CROSSINGSUBTYPE
DESCRIPTOR.enum_types_by_name['TurnType'] = _TURNTYPE
DESCRIPTOR.enum_types_by_name['AbRoadGridHeightLevel'] = _ABROADGRIDHEIGHTLEVEL
DESCRIPTOR.enum_types_by_name['AbRoadGridViewState'] = _ABROADGRIDVIEWSTATE
DESCRIPTOR.enum_types_by_name['QcFailType'] = _QCFAILTYPE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

VectorF = _reflection.GeneratedProtocolMessageType('VectorF', (_message.Message,), dict(
  DESCRIPTOR = _VECTORF,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.VectorF)
  ))
_sym_db.RegisterMessage(VectorF)

VectorD = _reflection.GeneratedProtocolMessageType('VectorD', (_message.Message,), dict(
  DESCRIPTOR = _VECTORD,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.VectorD)
  ))
_sym_db.RegisterMessage(VectorD)

ImgsizeInfo = _reflection.GeneratedProtocolMessageType('ImgsizeInfo', (_message.Message,), dict(
  DESCRIPTOR = _IMGSIZEINFO,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.ImgsizeInfo)
  ))
_sym_db.RegisterMessage(ImgsizeInfo)

PointF = _reflection.GeneratedProtocolMessageType('PointF', (_message.Message,), dict(
  DESCRIPTOR = _POINTF,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.PointF)
  ))
_sym_db.RegisterMessage(PointF)

PointD = _reflection.GeneratedProtocolMessageType('PointD', (_message.Message,), dict(
  DESCRIPTOR = _POINTD,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.PointD)
  ))
_sym_db.RegisterMessage(PointD)

PointFCloudInfo = _reflection.GeneratedProtocolMessageType('PointFCloudInfo', (_message.Message,), dict(
  DESCRIPTOR = _POINTFCLOUDINFO,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.PointFCloudInfo)
  ))
_sym_db.RegisterMessage(PointFCloudInfo)

Translation = _reflection.GeneratedProtocolMessageType('Translation', (_message.Message,), dict(
  DESCRIPTOR = _TRANSLATION,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.Translation)
  ))
_sym_db.RegisterMessage(Translation)

Quaternion = _reflection.GeneratedProtocolMessageType('Quaternion', (_message.Message,), dict(
  DESCRIPTOR = _QUATERNION,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.Quaternion)
  ))
_sym_db.RegisterMessage(Quaternion)

PoseInfo = _reflection.GeneratedProtocolMessageType('PoseInfo', (_message.Message,), dict(
  DESCRIPTOR = _POSEINFO,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.PoseInfo)
  ))
_sym_db.RegisterMessage(PoseInfo)

PolygonDType = _reflection.GeneratedProtocolMessageType('PolygonDType', (_message.Message,), dict(
  DESCRIPTOR = _POLYGONDTYPE,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.PolygonDType)
  ))
_sym_db.RegisterMessage(PolygonDType)

LidarCameraInfo = _reflection.GeneratedProtocolMessageType('LidarCameraInfo', (_message.Message,), dict(
  DESCRIPTOR = _LIDARCAMERAINFO,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.LidarCameraInfo)
  ))
_sym_db.RegisterMessage(LidarCameraInfo)

DataCompleteness = _reflection.GeneratedProtocolMessageType('DataCompleteness', (_message.Message,), dict(
  DESCRIPTOR = _DATACOMPLETENESS,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.DataCompleteness)
  ))
_sym_db.RegisterMessage(DataCompleteness)

MetricSupplementInfo = _reflection.GeneratedProtocolMessageType('MetricSupplementInfo', (_message.Message,), dict(
  DESCRIPTOR = _METRICSUPPLEMENTINFO,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.MetricSupplementInfo)
  ))
_sym_db.RegisterMessage(MetricSupplementInfo)

FusionSupplement = _reflection.GeneratedProtocolMessageType('FusionSupplement', (_message.Message,), dict(
  DESCRIPTOR = _FUSIONSUPPLEMENT,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.FusionSupplement)
  ))
_sym_db.RegisterMessage(FusionSupplement)

CameraSupplement = _reflection.GeneratedProtocolMessageType('CameraSupplement', (_message.Message,), dict(
  DESCRIPTOR = _CAMERASUPPLEMENT,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.CameraSupplement)
  ))
_sym_db.RegisterMessage(CameraSupplement)

LidarSupplement = _reflection.GeneratedProtocolMessageType('LidarSupplement', (_message.Message,), dict(
  DESCRIPTOR = _LIDARSUPPLEMENT,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.LidarSupplement)
  ))
_sym_db.RegisterMessage(LidarSupplement)

QualityCheckObjectInfo = _reflection.GeneratedProtocolMessageType('QualityCheckObjectInfo', (_message.Message,), dict(
  DESCRIPTOR = _QUALITYCHECKOBJECTINFO,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.QualityCheckObjectInfo)
  ))
_sym_db.RegisterMessage(QualityCheckObjectInfo)

QualityCheckFrameInfo = _reflection.GeneratedProtocolMessageType('QualityCheckFrameInfo', (_message.Message,), dict(
  DESCRIPTOR = _QUALITYCHECKFRAMEINFO,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.QualityCheckFrameInfo)
  ))
_sym_db.RegisterMessage(QualityCheckFrameInfo)

TrafficLightSupplementInfo = _reflection.GeneratedProtocolMessageType('TrafficLightSupplementInfo', (_message.Message,), dict(
  DESCRIPTOR = _TRAFFICLIGHTSUPPLEMENTINFO,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.TrafficLightSupplementInfo)
  ))
_sym_db.RegisterMessage(TrafficLightSupplementInfo)

BigMotObjectInfo = _reflection.GeneratedProtocolMessageType('BigMotObjectInfo', (_message.Message,), dict(
  DESCRIPTOR = _BIGMOTOBJECTINFO,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.BigMotObjectInfo)
  ))
_sym_db.RegisterMessage(BigMotObjectInfo)

VisibleInfo = _reflection.GeneratedProtocolMessageType('VisibleInfo', (_message.Message,), dict(
  DESCRIPTOR = _VISIBLEINFO,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.VisibleInfo)
  ))
_sym_db.RegisterMessage(VisibleInfo)

PreLabelInfo = _reflection.GeneratedProtocolMessageType('PreLabelInfo', (_message.Message,), dict(
  DESCRIPTOR = _PRELABELINFO,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.PreLabelInfo)
  ))
_sym_db.RegisterMessage(PreLabelInfo)

ManualLabelObjectInfo = _reflection.GeneratedProtocolMessageType('ManualLabelObjectInfo', (_message.Message,), dict(
  DESCRIPTOR = _MANUALLABELOBJECTINFO,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.ManualLabelObjectInfo)
  ))
_sym_db.RegisterMessage(ManualLabelObjectInfo)

ManualLabelFrameInfo = _reflection.GeneratedProtocolMessageType('ManualLabelFrameInfo', (_message.Message,), dict(
  DESCRIPTOR = _MANUALLABELFRAMEINFO,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.ManualLabelFrameInfo)
  ))
_sym_db.RegisterMessage(ManualLabelFrameInfo)

ManualQualityInspectionInfo = _reflection.GeneratedProtocolMessageType('ManualQualityInspectionInfo', (_message.Message,), dict(
  DESCRIPTOR = _MANUALQUALITYINSPECTIONINFO,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.ManualQualityInspectionInfo)
  ))
_sym_db.RegisterMessage(ManualQualityInspectionInfo)

DoorStatus = _reflection.GeneratedProtocolMessageType('DoorStatus', (_message.Message,), dict(
  DESCRIPTOR = _DOORSTATUS,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.DoorStatus)
  ))
_sym_db.RegisterMessage(DoorStatus)

LightSignal = _reflection.GeneratedProtocolMessageType('LightSignal', (_message.Message,), dict(
  DESCRIPTOR = _LIGHTSIGNAL,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.LightSignal)
  ))
_sym_db.RegisterMessage(LightSignal)

ReversingTimes = _reflection.GeneratedProtocolMessageType('ReversingTimes', (_message.Message,), dict(
  DESCRIPTOR = _REVERSINGTIMES,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.ReversingTimes)
  ))
_sym_db.RegisterMessage(ReversingTimes)

ReversingStatus = _reflection.GeneratedProtocolMessageType('ReversingStatus', (_message.Message,), dict(
  DESCRIPTOR = _REVERSINGSTATUS,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.ReversingStatus)
  ))
_sym_db.RegisterMessage(ReversingStatus)

CrossingStatus = _reflection.GeneratedProtocolMessageType('CrossingStatus', (_message.Message,), dict(
  DESCRIPTOR = _CROSSINGSTATUS,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.CrossingStatus)
  ))
_sym_db.RegisterMessage(CrossingStatus)

ObjectInfo = _reflection.GeneratedProtocolMessageType('ObjectInfo', (_message.Message,), dict(
  DESCRIPTOR = _OBJECTINFO,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.ObjectInfo)
  ))
_sym_db.RegisterMessage(ObjectInfo)

BlindSpotPointFInfo = _reflection.GeneratedProtocolMessageType('BlindSpotPointFInfo', (_message.Message,), dict(
  DESCRIPTOR = _BLINDSPOTPOINTFINFO,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.BlindSpotPointFInfo)
  ))
_sym_db.RegisterMessage(BlindSpotPointFInfo)

BlindSpotObsSupplementInfo = _reflection.GeneratedProtocolMessageType('BlindSpotObsSupplementInfo', (_message.Message,), dict(
  DESCRIPTOR = _BLINDSPOTOBSSUPPLEMENTINFO,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.BlindSpotObsSupplementInfo)
  ))
_sym_db.RegisterMessage(BlindSpotObsSupplementInfo)

ReconstructedObjectInfo = _reflection.GeneratedProtocolMessageType('ReconstructedObjectInfo', (_message.Message,), dict(
  DESCRIPTOR = _RECONSTRUCTEDOBJECTINFO,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.ReconstructedObjectInfo)
  ))
_sym_db.RegisterMessage(ReconstructedObjectInfo)

TrafficLightHeadInfo = _reflection.GeneratedProtocolMessageType('TrafficLightHeadInfo', (_message.Message,), dict(
  DESCRIPTOR = _TRAFFICLIGHTHEADINFO,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.TrafficLightHeadInfo)
  ))
_sym_db.RegisterMessage(TrafficLightHeadInfo)

Header = _reflection.GeneratedProtocolMessageType('Header', (_message.Message,), dict(
  DESCRIPTOR = _HEADER,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.Header)
  ))
_sym_db.RegisterMessage(Header)

LidarLabelMerticInfo = _reflection.GeneratedProtocolMessageType('LidarLabelMerticInfo', (_message.Message,), dict(
  DESCRIPTOR = _LIDARLABELMERTICINFO,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.LidarLabelMerticInfo)
  ))
_sym_db.RegisterMessage(LidarLabelMerticInfo)

ParseLabel = _reflection.GeneratedProtocolMessageType('ParseLabel', (_message.Message,), dict(
  DESCRIPTOR = _PARSELABEL,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.ParseLabel)
  ))
_sym_db.RegisterMessage(ParseLabel)

LidarLabelInfo = _reflection.GeneratedProtocolMessageType('LidarLabelInfo', (_message.Message,), dict(
  DESCRIPTOR = _LIDARLABELINFO,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.LidarLabelInfo)
  ))
_sym_db.RegisterMessage(LidarLabelInfo)

CameraLabelMerticInfo = _reflection.GeneratedProtocolMessageType('CameraLabelMerticInfo', (_message.Message,), dict(
  DESCRIPTOR = _CAMERALABELMERTICINFO,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.CameraLabelMerticInfo)
  ))
_sym_db.RegisterMessage(CameraLabelMerticInfo)

CameraLabelInfo = _reflection.GeneratedProtocolMessageType('CameraLabelInfo', (_message.Message,), dict(
  DESCRIPTOR = _CAMERALABELINFO,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.CameraLabelInfo)
  ))
_sym_db.RegisterMessage(CameraLabelInfo)

BlindSpotFrameInfo = _reflection.GeneratedProtocolMessageType('BlindSpotFrameInfo', (_message.Message,), dict(
  DESCRIPTOR = _BLINDSPOTFRAMEINFO,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.BlindSpotFrameInfo)
  ))
_sym_db.RegisterMessage(BlindSpotFrameInfo)

ImgQualityInfo = _reflection.GeneratedProtocolMessageType('ImgQualityInfo', (_message.Message,), dict(
  DESCRIPTOR = _IMGQUALITYINFO,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.ImgQualityInfo)
  ))
_sym_db.RegisterMessage(ImgQualityInfo)

ImgSubRegionQualityInfo = _reflection.GeneratedProtocolMessageType('ImgSubRegionQualityInfo', (_message.Message,), dict(
  DESCRIPTOR = _IMGSUBREGIONQUALITYINFO,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.ImgSubRegionQualityInfo)
  ))
_sym_db.RegisterMessage(ImgSubRegionQualityInfo)

AutoLabelingLabelInfo = _reflection.GeneratedProtocolMessageType('AutoLabelingLabelInfo', (_message.Message,), dict(
  DESCRIPTOR = _AUTOLABELINGLABELINFO,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.AutoLabelingLabelInfo)
  ))
_sym_db.RegisterMessage(AutoLabelingLabelInfo)

FrameInfo = _reflection.GeneratedProtocolMessageType('FrameInfo', (_message.Message,), dict(
  DESCRIPTOR = _FRAMEINFO,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.FrameInfo)
  ))
_sym_db.RegisterMessage(FrameInfo)

LabelInfo = _reflection.GeneratedProtocolMessageType('LabelInfo', (_message.Message,), dict(
  DESCRIPTOR = _LABELINFO,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.LabelInfo)
  ))
_sym_db.RegisterMessage(LabelInfo)

LabelInfos = _reflection.GeneratedProtocolMessageType('LabelInfos', (_message.Message,), dict(
  DESCRIPTOR = _LABELINFOS,
  __module__ = 'auto_labeling_label_pb2'
  # @@protoc_insertion_point(class_scope:idg.perception.autolabeling.LabelInfos)
  ))
_sym_db.RegisterMessage(LabelInfos)


_POINTFCLOUDINFO.fields_by_name['points_label']._options = None
_POINTFCLOUDINFO.fields_by_name['points_index']._options = None
_LIDARLABELMERTICINFO.fields_by_name['parsing_metric']._options = None
_PARSELABEL.fields_by_name['cls_parser']._options = None
_PARSELABEL.fields_by_name['cls_sbr']._options = None
_PARSELABEL.fields_by_name['dx']._options = None
_PARSELABEL.fields_by_name['dy']._options = None
_PARSELABEL.fields_by_name['dz']._options = None
_PARSELABEL.fields_by_name['in_roi']._options = None
_PARSELABEL.fields_by_name['index']._options = None
_PARSELABEL.fields_by_name['confidence_parser']._options = None
_PARSELABEL.fields_by_name['confidence_sbr']._options = None
_LIDARLABELINFO.fields_by_name['label']._options = None
_CAMERALABELINFO.fields_by_name['label']._options = None
# @@protoc_insertion_point(module_scope)
