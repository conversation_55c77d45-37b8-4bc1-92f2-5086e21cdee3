import os
import sys
import argparse
import requests
import json


class TaskFlow:
    permanent_task_list = [
        'mark_flow_online_v1', #["autolabel_model","autolabel_post"]
    ]
    EXTRACT_TASK_TYPE = "extractor"#解算
    STREAM_TASK_TYPE = "write_warehouse"#入库,配置中增加batch_num_pre字段
    AL_TASK_TYPE= "mark"#标注

    #http://10.25.67.136:8089/tmbatch_task/flow/get_dataset_status?id=101
    #http://10.25.67.136:8082/tmbatch_task/flow/get_dataset_status?id=101

    @staticmethod
    def submit_temp_task_flow(config_file, task_file, dataset_name, flow_name, dataset_tranfer_rule):
        # url = "http://10.25.67.136:8089/tmbatch_task/flow/once_run_dataset"
        url = "http://10.25.67.136:8089/tmbatch_task/flow/submit_task"
        payload = {
            "dataset_name": str(dataset_name),
            "flow_name": str(flow_name),
            "creater": "sunbaofeng",
        }
        flow_config = []
        rule =[]
        for step_name, step_type in dataset_tranfer_rule.items():
            step = {}
            step["step_name"] = str(step_name)
            step["step_type"] = str(step_type)
            step["schedule_interval"] = "0 */1 * * *"
            step["batch_size"] = 1000

            flow_config.append(step)
            rule.append(str(step_name))

        payload["flow_config"] = json.dumps(flow_config)
        payload["dataset_tranfer_rule"] = json.dumps(rule)

        files=[
            ('dataset_batch_config',open(config_file,'rb')),
            ('dataset_file',open(task_file,'rb')),
        ]
        headers = {}

        response = requests.request("POST", url, headers=headers, data=payload, files=files)
        print(response.text)

    @staticmethod
    def create_permanent_task_flow(flow_name):
        url = "http://10.25.67.136:8089/tmbatch_task/flow/create_flow"

        payload = json.dumps({
            "flow_name": flow_name,
            "flow_config": [
                {
                "step_name": "autolabel_model",
                "schedule_interval": "* */1 * * *",
                "batch_size": 1000
                },
                {
                "step_name": "autolabel_post",
                "schedule_interval": "* */1 * * *",
                "batch_size": 1000
                }
            ],
            "creater": "sunbaofeng"
        })

        headers = {
            'Content-Type': 'application/json'
        }
        response = requests.request("POST", url, headers=headers, data=payload)
        print(response.text)

    @staticmethod
    def submit_permanent_task_flow(config_file, task_file, dataset_name, flow_name, dataset_tranfer_rule):
        url = "http://10.25.67.136:8089/tmbatch_task/flow/add_dataset"
        rule = []
        for item in dataset_tranfer_rule:
            rule.append(str(item))

        payload = {
            "dataset_name": str(dataset_name),
            "flow_name": str(flow_name),
            "creater": "sunbaofeng",
            'dataset_tranfer_rule': json.dumps(rule)
        }
        files=[
            ('dataset_batch_config',open(config_file,'rb')),
            ('dataset_file',open(task_file,'rb'))
        ]
        headers = {}
        response = requests.request("POST", url, headers=headers, data=payload, files=files)
        print(response.text)

    @staticmethod
    def stop_task_flow(flow_name):
        # url = "http://10.25.67.136:8089/tmbatch_task/flow/delete_flow"
        url = "http://10.25.67.136:8089/tmbatch_task/flow/delete_flow"

        payload = json.dumps({
            "flow_name": flow_name,
            "creater": "sunbaofeng"
        })

        headers = {
            'Content-Type': 'application/json'
        }
        response = requests.request("POST", url, headers=headers, data=payload)
        print(response.text)

if __name__ == '__main__':

    config_file = "./new_produce.json"
    task_file = "./Collision_Filter.txt"

    dataset_name = "produ_collision_filter_20250731" #recommend: name_time_dataset
    flow_name = "flow_" + dataset_name
    # dataset_tranfer_rule = {"autolabel_model":TaskFlow.AL_TASK_TYPE}
    dataset_tranfer_rule = {"autolabel_post":TaskFlow.AL_TASK_TYPE}
    # dataset_tranfer_rule = {"autolabel_model":TaskFlow.AL_TASK_TYPE,"autolabel_post":TaskFlow.AL_TASK_TYPE}
    # dataset_tranfer_rule = {"autolabel_model":TaskFlow.AL_TASK_TYPE,"autolabel_vis":TaskFlow.AL_TASK_TYPE,"autolabel_post":TaskFlow.AL_TASK_TYPE}
    # dataset_tranfer_rule = {"autolabel_extract":TaskFlow.EXTRACT_TASK_TYPE}
    # dataset_tranfer_rule = {"autolabel_stream":TaskFlow.STREAM_TASK_TYPE}
    # dataset_tranfer_rule = {"autolabel_extract":TaskFlow.EXTRACT_TASK_TYPE,"autolabel_stream":TaskFlow.STREAM_TASK_TYPE}
    # dataset_tranfer_rule = {"autolabel_extract":TaskFlow.EXTRACT_TASK_TYPE,"autolabel_stream":TaskFlow.STREAM_TASK_TYPE,"autolabel_model":TaskFlow.AL_TASK_TYPE}

    TaskFlow.submit_temp_task_flow(config_file, task_file, dataset_name, flow_name, dataset_tranfer_rule) #submit temp task
    # TaskFlow.stop_task_flow("flow_tmp_fisheye_extract_250310_1741659262333")
    # TaskFlow.submit_permanent_task_flow(config_file, task_file, dataset_name, flow_name, dataset_tranfer_rule) #submit permanent task
















