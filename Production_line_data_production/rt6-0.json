{"autolabel_model": {"batch_program_entry": "/home/<USER>/autolabel/install/start_autolabel.sh", "batch_program_name": "run_auto_labeling_tool.py,autolabel_main.py,start_autolabel.sh,run_pipeline.py", "batch_subtype": "labelGpu", "name": "rou<PERSON>che_gt_json_production_250725_qsz", "platform": "normandy", "batch_type": "auto_label", "email": "<EMAIL>", "image": "weekly-sim-10_2_1055_1-cuda-1_0_3_1-baseos-5_0_8_4-20230721-000132_3", "pass_params": {"batch_max_retry_times": 0, "batch_job_priority": "VERY_HIGH", "batch_image_registry": "iregistry.baidu-int.com", "batch_image_module": "ad-simulation-develop/simulationworker", "registry_ugi_user": "liyongliang", "registry_ugi_token": "MhxzKhl8", "MAX_PLAY_TIME": 14400, "batch_gpu_mem": 11000, "batch_max_capacity": 100, "batch_task_capacity": 100}, "replace_binary_set": [{"type": "afs", "name": "patch1", "url": "/user/ad-sim-eng/simulate/20230908/test_20230908101724.692927.r160273.tgz"}, {"type": "wget", "name": "3c7374c", "url": "wget -O output.tar.gz --no-check-certificate --header \"IREPO-TOKEN:71aa5c15-03b5-430d-9157-461ab5af38e6\" \"https://irepo.baidu-int.com/rest/prod/v3/baidu/adu-adfm/adfm-labeling/nodes/83861134/files\""}], "job_list": [{"tm_batch_cmd": "./bin/auto_labeling_taskflow_anp --cfg ./cfgs/cpp_taskflow_l4_final.yaml --bos_root \"bos:/l4-adfm-rt6/auto-labeling/\" --l4_src_version default --l4_main_lidar_name at128_fusion --pure_version roujiche_gt_json_250725_qsz --task_id "}]}, "autolabel_post": {"batch_program_entry": "/home/<USER>/autolabel/install/start_autolabel.sh", "batch_program_name": "run_auto_labeling_tool.py,autolabel_main.py,start_autolabel.sh,run_pipeline.py", "batch_subtype": "al_streaming", "name": "rou<PERSON>che_gt_json_production_250725_qsz", "platform": "normandy", "batch_type": "auto_label", "email": "<EMAIL>", "image": "weekly-sim-10_2_1055_1-cuda-1_0_3_1-baseos-5_0_8_4-20230721-000132_3", "pass_params": {"batch_max_retry_times": 0, "batch_job_priority": "HIGH", "batch_image_registry": "iregistry.baidu-int.com", "batch_image_module": "ad-simulation-develop/simulationworker", "registry_ugi_user": "liyongliang", "registry_ugi_token": "MhxzKhl8", "MAX_PLAY_TIME": 14400, "batch_max_capacity": 100, "batch_task_capacity": 100, "batch_cpu_mem": 40000}, "replace_binary_set": [{"type": "afs", "name": "patch1", "url": "/user/ad-sim-eng/simulate/20230908/test_20230908101724.692927.r160273.tgz"}, {"type": "wget", "name": "3c7374c", "url": "wget -O output.tar.gz --no-check-certificate --header \"IREPO-TOKEN:71aa5c15-03b5-430d-9157-461ab5af38e6\" \"https://irepo.baidu-int.com/rest/prod/v3/baidu/adu-adfm/adfm-labeling/nodes/83861134/files\""}], "job_list": [{"tm_batch_cmd": "./bin/auto_labeling_taskflow_anp --cfg ./cfgs/cpp_taskflow_l4_final.yaml --bos_root \"bos:/l4-adfm-rt6/auto-labeling/\" --l4_src_version default --l4_main_lidar_name at128_fusion --pure_version roujiche_gt_json_250725_qsz --task_id "}]}}