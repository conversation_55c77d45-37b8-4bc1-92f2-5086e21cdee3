{"autolabel_post": {"batch_program_entry": "/home/<USER>/autolabel_anp/install/start_autolabel.sh", "batch_program_name": "autolabel_main.py,start_autolabel.sh,auto_labeling_taskflow_anp", "batch_subtype": "al_streaming", "name": "roujiche_data_re_produce_20250801", "platform": "normandy", "batch_type": "auto_label", "email": "<EMAIL>", "image": "weekly-sim-10_2_1055_1-baseos-anp-autolabel-20250123-000003", "pass_params": {"batch_max_retry_times": 0, "batch_job_priority": "HIGH", "batch_image_registry": "iregistry.baidu-int.com", "batch_image_module": "ad-simulation-develop/simulationworker", "registry_ugi_user": "liyongliang", "registry_ugi_token": "MhxzKhl8", "MAX_PLAY_TIME": 7200, "batch_max_capacity": 100, "batch_task_capacity": 100, "batch_cpu_mem": 40000}, "replace_binary_set": [{"type": "afs", "name": "patch1", "url": "/user/ad-sim-eng/simulate/20230908/test_20230908101724.692927.r160273.tgz"}, {"type": "wget", "name": "3c7374c", "url": "wget -O output.tar.gz --no-check-certificate --header \"IREPO-TOKEN:5c4196b7-66ea-4daf-ba0a-29d201cf1e72\" \"https://irepo.baidu-int.com/rest/prod/v3/baidu/adu-adfm/adfm-labeling/releases/500.0.12.1/files\""}], "job_list": [{"tm_batch_cmd": "./bin/auto_labeling_taskflow_anp --cfg cfgs/cpp_taskflow_l4_final_velocity.yaml --bos_root bos:/l4-adfm-rt6/auto-labeling/ --l4_src_version default --l4_main_lidar_name at128_fusion --pure_version 500.12.0.1_motion  --l4_src_manual_version empty --task_id"}]}}