{"autolabel_post": {"batch_program_entry": "/home/<USER>/autolabel_anp/install/start_autolabel.sh", "batch_program_name": "autolabel_main.py,start_autolabel.sh,auto_labeling_taskflow_anp", "batch_subtype": "al_streaming", "name": "produ_collision_filter_20250731", "platform": "normandy", "batch_type": "auto_label", "email": "<EMAIL>", "image": "weekly-sim-10_2_1055_1-baseos-anp-autolabel-20250123-000003", "pass_params": {"batch_max_retry_times": 0, "batch_job_priority": "HIGH", "batch_image_registry": "iregistry.baidu-int.com", "batch_image_module": "ad-simulation-develop/simulationworker", "registry_ugi_user": "liyongliang", "registry_ugi_token": "MhxzKhl8", "MAX_PLAY_TIME": 7200, "batch_max_capacity": 100, "batch_task_capacity": 100, "batch_cpu_mem": 40000}, "replace_binary_set": [{"type": "afs", "name": "patch1", "url": "/user/ad-sim-eng/simulate/20230908/test_20230908101724.692927.r160273.tgz"}, {"type": "wget", "name": "3c7374c", "url": "wget -O output.tar.gz --no-check-certificate --header \"IREPO-TOKEN:71aa5c15-03b5-430d-9157-461ab5af38e6\" \"https://irepo.baidu-int.com/rest/prod/v3/baidu/adu-adfm/adfm-labeling/nodes/84165552/files\""}], "job_list": [{"tm_batch_cmd": "./bin/auto_labeling_taskflow_anp --cfg ./cfgs/cpp_taskflow_l4_final.yaml --bos_root \"bos:/labeling-inner/auto-labeling/\" --l4_src_version default --l4_main_lidar_name at128_fusion --pure_version produ_collision_filter_20250731 --task_id "}]}}