import os
import requests
import json
import argparse
import yaml
import fire

def stop_task_flow(id):
    url = "http://10.25.67.136:8089/tmbatch_task/flow/delete_flow"

    payload = json.dumps({
        "dataset_id": id,
        "creater": "qishouzheng"
    })

    headers = {
        'Content-Type': 'application/json'
    }
    response = requests.request("POST", url, headers=headers, data=payload)
    print(response.text) 

if __name__ == '__main__':
    dataset_id = 6063
    stop_task_flow(dataset_id)