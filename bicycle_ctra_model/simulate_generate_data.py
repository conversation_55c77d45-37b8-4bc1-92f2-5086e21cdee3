import os
import random
import math
from typing import List, Dict

# 创建保存文件的目录
output_dir = "./synthetic_autonomous_driving_data"
os.makedirs(output_dir, exist_ok=True)

# 定义目标的初始状态范围
MAX_VEHICLES = 20  # 最大可能的车辆数
MIN_VEHICLES = 5   # 最少可能的车辆数

# 车辆属性的范围
WIDTH_RANGE = (1.5, 2.5)  # 宽度范围（米）
LENGTH_RANGE = (3.0, 5.0)  # 长度范围（米）
SPEED_RANGE = (0, 30)  # 速度范围（米/秒）
ACCELERATION_RANGE = (-3, 3)  # 加速度范围（米/秒^2）
TURNING_ANGLE_RANGE = (-30, 30)  # 前轮转向角范围（度）
TURNING_ANGLE_VELOCITY_RANGE = (-10, 10)  # 转向角速度范围（度/秒）
X_RANGE = (-50, 50)  # 自车坐标系下 X 坐标范围（米）
Y_RANGE = (-50, 50)  # 自车坐标系下 Y 坐标范围（米）

# 自行车模型的车辆参数
AXLE_LENGTH = 2.5  # 车轴距（米）
DELTA_T = 0.1  # 每帧时间间隔（秒）

# 帧数
NUM_FRAMES = 100

# 初始化目标跟踪 ID
current_tracking_id = 0


def generate_vehicle_data(tracking_id: int) -> Dict:
    """生成单个车辆的初始属性"""
    width = round(random.uniform(*WIDTH_RANGE), 2)
    length = round(random.uniform(*LENGTH_RANGE), 2)
    orientation = round(random.uniform(0, 360), 2)  # 朝向角度（0-360度）
    x = round(random.uniform(*X_RANGE), 2)
    y = round(random.uniform(*Y_RANGE), 2)
    speed = round(random.uniform(*SPEED_RANGE), 2)
    acceleration = round(random.uniform(*ACCELERATION_RANGE), 2)
    turning_angle = round(random.uniform(*TURNING_ANGLE_RANGE), 2)  # 前轮转向角（度）
    turning_angle_velocity = round(random.uniform(*TURNING_ANGLE_VELOCITY_RANGE), 2)  # 转向角速度（度/秒）

    return {
        "tracking_id": tracking_id,
        "width": width,
        "length": length,
        "orientation": orientation,
        "x": x,
        "y": y,
        "speed": speed,
        "acceleration": acceleration,
        "turning_angle": turning_angle,  # 以度为单位
        "turning_angle_velocity": turning_angle_velocity,  # 转向角速度（度/秒）
    }


def update_vehicle_data(vehicle: Dict) -> Dict:
    """使用自行车模型更新车辆运动状态"""
    # 将转向角从度转换为弧度
    delta = math.radians(vehicle["turning_angle"])

    # 更新速度（速度由加速度积分得到）
    vehicle["speed"] += vehicle["acceleration"] * DELTA_T
    vehicle["speed"] = max(0, vehicle["speed"])  # 速度不能为负

    # 计算转弯半径（避免 tan(delta) = 0 的情况）
    if abs(delta) > 1e-4:  # 如果转向角不为 0
        turn_radius = AXLE_LENGTH / math.tan(delta)
    else:
        turn_radius = float('inf')  # 直线行驶时，转弯半径趋于无穷大

    # 更新朝向角（orientation）
    if turn_radius != float('inf'):
        delta_orientation = (vehicle["speed"] / turn_radius) * DELTA_T  # 朝向角变化（弧度）
    else:
        delta_orientation = 0

    vehicle["orientation"] += math.degrees(delta_orientation)  # 弧度转为度
    vehicle["orientation"] %= 360  # 限制在 0-360 度范围内

    # 更新位置
    delta_x = vehicle["speed"] * math.cos(math.radians(vehicle["orientation"])) * DELTA_T
    delta_y = vehicle["speed"] * math.sin(math.radians(vehicle["orientation"])) * DELTA_T
    vehicle["x"] += delta_x
    vehicle["y"] += delta_y

    # 更新转向角速度（平滑变化）
    target_turning_angle_velocity = random.uniform(*TURNING_ANGLE_VELOCITY_RANGE)
    vehicle["turning_angle_velocity"] += (target_turning_angle_velocity - vehicle["turning_angle_velocity"]) * 0.1

    # 更新转向角（由转向角速度积分得到）
    vehicle["turning_angle"] += vehicle["turning_angle_velocity"] * DELTA_T
    vehicle["turning_angle"] = max(
        min(vehicle["turning_angle"], TURNING_ANGLE_RANGE[1]),
        TURNING_ANGLE_RANGE[0]
    )

    # 模拟加速度的平滑变化
    target_acceleration = random.uniform(*ACCELERATION_RANGE)
    vehicle["acceleration"] += (target_acceleration - vehicle["acceleration"]) * 0.1

    return vehicle


def save_frame_data(frame: int, vehicles: List[Dict]):
    """将每帧数据保存为txt文件"""
    file_path = os.path.join(output_dir, f"{frame:05d}.txt")
    with open(file_path, "w") as f:
        for vehicle in vehicles:
            orientation_radians = math.radians(vehicle["orientation"])
            turning_angle_radians = math.radians(vehicle["turning_angle"])
            turning_angle_velocity_radians = math.radians(vehicle["turning_angle_velocity"])
            line = (
                f"{vehicle['tracking_id']} "
                f"{vehicle['width']} {vehicle['length']} {orientation_radians:.6f} "
                f"{vehicle['x']:.2f} {vehicle['y']:.2f} {vehicle['speed']:.2f} "
                f"{vehicle['acceleration']:.2f} {turning_angle_radians:.6f} "
                f"{turning_angle_velocity_radians:.6f}\n"  # 保存转向角速度（弧度/秒）
            )
            f.write(line)


def main():
    global current_tracking_id

    # 初始化车辆列表
    vehicles = [generate_vehicle_data(current_tracking_id + i) for i in range(random.randint(MIN_VEHICLES, MAX_VEHICLES))]
    current_tracking_id += len(vehicles)

    for frame in range(1, NUM_FRAMES + 1):
        # 更新每个车辆的状态
        vehicles = [update_vehicle_data(vehicle) for vehicle in vehicles]

        # 模拟车辆的出现和消失
        if random.random() < 0.1:  # 10%概率添加新车辆
            vehicles.append(generate_vehicle_data(current_tracking_id))
            current_tracking_id += 1
        if random.random() < 0.05 and len(vehicles) > MIN_VEHICLES:  # 5%概率移除旧车辆
            vehicles.pop(random.randint(0, len(vehicles) - 1))

        # 保存当前帧数据
        save_frame_data(frame, vehicles)

    print(f"生成的虚拟数据已保存到: {output_dir}")


if __name__ == "__main__":
    main()