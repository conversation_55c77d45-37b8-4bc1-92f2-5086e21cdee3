import numpy as np

class CV_Model:
    def __init__(self, initial_state):
        """
        Initialize the CV model.
        :param initial_state: Initial state [x, y, v, θ]
        """
        if len(initial_state) != 4:
            raise ValueError("Initial state must have 4 elements: [x, y, v, θ]")
        self.state = np.array(initial_state, dtype=float)

    def predict(self, delta_t, num_steps):
        """
        Perform extrapolation using the CV model.
        :param delta_t: Time interval Δt
        :param num_steps: Number of time steps for extrapolation
        :return: Predicted state sequence, including the state at each time step
        """
        if delta_t <= 0:
            raise ValueError("delta_t must be positive")
        states = [self.state.copy()]
        for _ in range(num_steps):
            self._update(delta_t)
            states.append(self.state.copy())
        return np.array(states)

    def _update(self, delta_t):
        """
        Update the state variables.
        :param delta_t: Time interval Δt
        """
        x, y, v, theta = self.state

        # Update position based on constant velocity model
        x_new = x + v * np.cos(theta) * delta_t
        y_new = y + v * np.sin(theta) * delta_t

        # In CV model, velocity and direction remain constant
        v_new = v
        theta_new = theta

        self.state = np.array([x_new, y_new, v_new, theta_new], dtype=float)

if __name__ == "__main__":

    # Initial state: (x, y, v, θ)
    initial_state = [0, 0, 10, np.pi / 4]  # x=0, y=0, v=10 m/s, θ=45°

    cv = CV_Model(initial_state)

    delta_t = 0.1  # Time step (seconds)
    num_steps = 10  # Number of time steps for prediction

    predicted_states = cv.predict(delta_t, num_steps)

    for i, state in enumerate(predicted_states):
        print(f"Step {i}: x={state[0]:.2f}, y={state[1]:.2f}, v={state[2]:.2f}, θ={state[3]:.2f}")