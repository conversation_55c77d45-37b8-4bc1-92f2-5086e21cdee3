import numpy as np

class CTRA_Model:
    def __init__(self, initial_state):
        """
        Initialize the CTRA model.
        :param initial_state: Initial state [x, y, v, θ, a, ω]
        """
        if len(initial_state) != 6:
            raise ValueError("Initial state must have 6 elements: [x, y, v, θ, a, ω]")
        self.state = np.array(initial_state, dtype=float)

    def predict(self, delta_t, num_steps):
        """
        Perform extrapolation using the CTRA model.
        :param delta_t: Time interval Δt
        :param num_steps: Number of time steps for extrapolation
        :return: Predicted state sequence, including the state at each time step
        """
        if delta_t <= 0:
            raise ValueError("delta_t must be positive")
        states = [self.state.copy()]
        for _ in range(num_steps):
            self._update(delta_t)
            states.append(self.state.copy())
        return np.array(states)

    def _update(self, delta_t):
        """
        Update the state variables.
        :param delta_t: Time interval Δt
        """
        x, y, v, theta, a, omega = self.state

        if np.abs(omega) > 1e-6:  # ω ≠ 0
            x_new = x + (v / omega) * (np.sin(theta + omega * delta_t) - np.sin(theta))
            y_new = y + (v / omega) * (-np.cos(theta + omega * delta_t) + np.cos(theta))
            theta_new = theta + omega * delta_t
        else:  # ω ≈ 0
            x_new = x + v * np.cos(theta) * delta_t + 0.5 * a * delta_t**2 * np.cos(theta)
            y_new = y + v * np.sin(theta) * delta_t + 0.5 * a * delta_t**2 * np.sin(theta)
            theta_new = theta

        v_new = v + a * delta_t
        self.state = np.array([x_new, y_new, v_new, theta_new, a, omega], dtype=float)

if __name__ == "__main__":

    # (x,y,v,θ,a,ω)
    initial_state = [0, 0, 10, np.pi / 4, 1, 0.1] 

    ctra = CTRA_Model(initial_state)

    delta_t = 0.1 
    num_steps = 10  

    predicted_states = ctra.predict(delta_t, num_steps)

    for i, state in enumerate(predicted_states):
        print(f"Step {i}: x={state[0]:.2f}, y={state[1]:.2f}, v={state[2]:.2f}, θ={state[3]:.2f}, a={state[4]:.2f}, ω={state[5]:.2f}")

    