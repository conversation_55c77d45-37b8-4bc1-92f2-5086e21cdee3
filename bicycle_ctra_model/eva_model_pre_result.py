import os
import numpy as np
import math
from typing import List, Dict
from cv_model import CV_Model
from ctra_model import CTRA_Model
from bicycle_model import BicycleModel
from shapely.geometry import Polygon


def load_frame_data(frame: int, data_dir: str) -> List[Dict]:
    """
    从指定帧文件中加载车辆数据
    :param frame: 帧序号
    :param data_dir: 数据目录
    :return: 车辆数据列表
    """
    # 构造文件路径
    file_path = os.path.join(data_dir, f"{frame:05d}.txt")
    # 检查文件是否存在
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件 {file_path} 不存在！")
    
    vehicles = []
    with open(file_path, "r") as f:
        for line in f:
            values = line.strip().split()
            vehicle = {
                "tracking_id": int(values[0]),
                "width": float(values[1]),
                "length": float(values[2]),
                "orientation": float(values[3]),  # 已经是弧度制
                "x": float(values[4]),
                "y": float(values[5]),
                "v": float(values[6]),
                "a": float(values[7]),
                "turn_angle": float(values[8]),
                "w": float(values[9]),
            }
            vehicles.append(vehicle)
    return vehicles


def calculate_iou(predicted: Dict, actual: Dict) -> float:
    """
    计算两个目标的 IOU 值
    :param predicted: 预测的目标数据
    :param actual: 实际的目标数据
    :return: IOU 值
    """
    pred_x, pred_y, pred_width, pred_length = predicted["x"], predicted["y"], predicted["width"], predicted["length"]
    actual_x, actual_y, actual_width, actual_length = actual["x"], actual["y"], actual["width"], actual["length"]

    # 计算预测框和实际框的四个边界
    pred_x1, pred_y1 = pred_x - pred_width / 2, pred_y - pred_length / 2
    pred_x2, pred_y2 = pred_x + pred_width / 2, pred_y + pred_length / 2

    actual_x1, actual_y1 = actual_x - actual_width / 2, actual_y - actual_length / 2
    actual_x2, actual_y2 = actual_x + actual_width / 2, actual_y + actual_length / 2

    # 计算交集区域
    inter_x1 = max(pred_x1, actual_x1)
    inter_y1 = max(pred_y1, actual_y1)
    inter_x2 = min(pred_x2, actual_x2)
    inter_y2 = min(pred_y2, actual_y2)

    inter_area = max(0, inter_x2 - inter_x1) * max(0, inter_y2 - inter_y1)

    # 计算并集区域
    pred_area = (pred_x2 - pred_x1) * (pred_y2 - pred_y1)
    actual_area = (actual_x2 - actual_x1) * (actual_y2 - actual_y1)
    union_area = pred_area + actual_area - inter_area

    # 计算 IOU
    if union_area == 0:
        return 0.0
    return inter_area / union_area


def calculate_ro_iou(predicted: Dict, actual: Dict) -> float:
    """
    计算两个旋转目标的 IOU 值
    :param predicted: 预测的目标数据
    :param actual: 实际的目标数据
    :return: IOU 值
    """
    def get_rotated_corners(x, y, width, length, orientation):
        """
        根据中心点、宽度、长度和旋转角计算矩形的四个顶点坐标
        """
        # 半宽和半长
        half_width, half_length = width / 2, length / 2

        # 旋转角度（弧度制）
        theta = orientation

        # 矩形四个顶点相对于中心点的坐标（未旋转）
        corners = [
            (-half_width, -half_length),
            (-half_width, half_length),
            (half_width, half_length),
            (half_width, -half_length)
        ]

        # 计算旋转后的顶点坐标
        rotated_corners = []
        for corner_x, corner_y in corners:
            rotated_x = x + (corner_x * math.cos(theta) - corner_y * math.sin(theta))
            rotated_y = y + (corner_x * math.sin(theta) + corner_y * math.cos(theta))
            rotated_corners.append((rotated_x, rotated_y))
        return rotated_corners

    # 获取预测框和实际框的旋转矩形顶点
    pred_corners = get_rotated_corners(
        predicted["x"], predicted["y"], predicted["width"], predicted["length"], predicted["orientation"]
    )
    actual_corners = get_rotated_corners(
        actual["x"], actual["y"], actual["width"], actual["length"], actual["orientation"]
    )

    # 构建旋转矩形的多边形
    pred_polygon = Polygon(pred_corners)
    actual_polygon = Polygon(actual_corners)

    # 计算交集和并集区域
    inter_area = pred_polygon.intersection(actual_polygon).area
    union_area = pred_polygon.union(actual_polygon).area

    # 计算 IOU
    if union_area == 0:
        return 0.0
    return inter_area / union_area


def process_frame(frame: int, n: int, delta_t: float, data_dir: str, model_type: str) -> (float, int):
    """
    处理单帧数据，计算预测的车辆与实际车辆的 IOU。
    :param frame: 当前帧号
    :param n: 预测的帧数
    :param delta_t: 时间步长
    :param data_dir: 数据目录
    :return: 当前帧的总 IOU 和计数器
    """
    # 加载当前帧和未来第 n 帧的数据
    current_vehicles = load_frame_data(frame, data_dir)
    future_vehicles = load_frame_data(frame + n, data_dir)

    # 当前帧的总 IOU 和计数器
    frame_total_iou = 0.0
    frame_count = 0

    # 遍历所有车辆
    for vehicle in current_vehicles:
        if model_type == "CV":
            # 初始化 CV 模型
            initial_state = [vehicle["x"], vehicle["y"], vehicle["v"], vehicle["orientation"]]
            cv_model = CV_Model(initial_state)

            # 预测 n 帧后的状态
            predicted_states = cv_model.predict(delta_t, n)
            predicted_state = predicted_states[-1]  # 获取第 n 帧的预测状态
        elif model_type == "CTRA":
            # 初始化 CTRA 模型
            initial_state = [vehicle["x"], vehicle["y"], vehicle["v"], vehicle["orientation"], vehicle["a"], vehicle["w"]]
            ctra_model = CTRA_Model(initial_state)

            # 预测 n 帧后的状态
            predicted_states = ctra_model.predict(delta_t, n)
            predicted_state = predicted_states[-1]  # 获取第 n 帧的预测状态
        elif model_type == "Bicycle":
            # 初始化自行车模型
            initial_state = [vehicle["x"], vehicle["y"], vehicle["v"], vehicle["orientation"], vehicle["turn_angle"], vehicle["a"]]
            bicycle_model = BicycleModel(initial_state, 2.5)

            # 预测 n 帧后的状态
            predicted_states = bicycle_model.predict(delta_t, n)
            predicted_state = predicted_states[-1]  # 获取第 n 帧的预测状态
        else:
            raise ValueError("Invalid model type.")

        # 构造预测目标
        predicted_vehicle = {
            "x": predicted_state[0],
            "y": predicted_state[1],
            "width": vehicle["width"],
            "length": vehicle["length"],
            "orientation": predicted_state[3]
        }

        # 找到对应的实际目标
        actual_vehicle = next((v for v in future_vehicles if v["tracking_id"] == vehicle["tracking_id"]), None)
        if actual_vehicle is not None:
            # 计算 IOU
            iou = calculate_ro_iou(predicted_vehicle, actual_vehicle)
            frame_total_iou += iou
            frame_count += 1

    return frame_total_iou, frame_count


def main():
    # 数据目录和参数
    data_dir = "./synthetic_autonomous_driving_data"
    delta_t = 0.1  # 时间步长
    n = 5  # 预测的帧数

    # 总 IOU 和计数器
    total_iou = 0.0
    total_count = 0
    NUM_FRAMES = 100
    # mode = "CV"
    # mode = "CTRA"
    mode = "Bicycle"

    # 遍历所有帧
    for frame in range(1, NUM_FRAMES - n + 1):  # 确保预测后还有实际数据
        frame_iou, frame_count = process_frame(frame, n, delta_t, data_dir, mode)
        total_iou += frame_iou
        total_count += frame_count

    # 计算平均 IOU
    mean_iou = total_iou / total_count if total_count > 0 else 0.0
    print(f"平均 IOU: {mean_iou:.4f}")


if __name__ == "__main__":
    main()