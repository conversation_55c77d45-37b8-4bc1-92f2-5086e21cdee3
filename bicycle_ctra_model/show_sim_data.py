import os
import cv2
import numpy as np
import random

# 数据文件夹路径
data_dir = "synthetic_autonomous_driving_data"

# 检查数据目录是否存在
if not os.path.exists(data_dir):
    raise FileNotFoundError(f"数据目录 {data_dir} 不存在，请先运行生成数据的脚本！")

# 定义函数，用于加载所有帧的数据
def load_all_frames():
    """加载所有帧的数据"""
    all_vehicles = {}
    for frame in range(1, 101):  # 遍历所有帧
        file_path = os.path.join(data_dir, f"{frame:05d}.txt")
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件 {file_path} 不存在！")
        
        with open(file_path, "r") as f:
            for line in f:
                values = line.strip().split()
                tracking_id = int(values[0])
                vehicle = {
                    "frame": frame,
                    "width": float(values[1]),
                    "length": float(values[2]),
                    "orientation": float(values[3]),
                    "x": float(values[4]),
                    "y": float(values[5]),
                    "speed": float(values[6]),
                    "acceleration": float(values[7]),
                    "turning_angle": float(values[8]),
                }
                if tracking_id not in all_vehicles:
                    all_vehicles[tracking_id] = []
                all_vehicles[tracking_id].append(vehicle)
    return all_vehicles

# 定义函数，用于绘制所有帧的轨迹
def plot_all_trajectories(vehicles_dict):
    """绘制所有车辆的轨迹到一张图片"""
    # 创建空白图像 (1200x1200 像素)，背景为白色
    img_size = 1200
    img = np.ones((img_size, img_size, 3), dtype=np.uint8) * 255

    # 坐标系的中心在图像中心
    origin = img_size // 2
    scale = 10  # 缩放比例（1米 = 10像素）

    # 绘制网格线
    for i in range(0, img_size, 100):
        color = (200, 200, 200)  # 灰色网格线
        cv2.line(img, (i, 0), (i, img_size), color, 1)
        cv2.line(img, (0, i), (img_size, i), color, 1)

    # 绘制坐标系的 X 和 Y 轴
    cv2.line(img, (origin, 0), (origin, img_size), (0, 0, 0), 2)  # Y 轴
    cv2.line(img, (0, origin), (img_size, origin), (0, 0, 0), 2)  # X 轴

    # 为每个 track_id 分配一个随机颜色
    track_colors = {}
    for track_id in vehicles_dict.keys():
        track_colors[track_id] = (
            random.randint(0, 255),
            random.randint(0, 255),
            random.randint(0, 255),
        )

    # 绘制每辆车的轨迹
    for track_id, trajectory in vehicles_dict.items():
        color = track_colors[track_id]
        prev_x, prev_y = None, None

        for vehicle in trajectory:
            x, y = vehicle["x"], vehicle["y"]
            # 转换为图像坐标 (像素)
            img_x = origin + int(x * scale)
            img_y = origin - int(y * scale)

            # 绘制轨迹点
            cv2.circle(img, (img_x, img_y), 3, color, -1)

            # 如果有上一点，绘制轨迹线
            if prev_x is not None and prev_y is not None:
                cv2.line(img, (prev_x, prev_y), (img_x, img_y), color, 2)

            # 更新上一点
            prev_x, prev_y = img_x, img_y

        # 标注 track_id
        if trajectory:
            last_vehicle = trajectory[-1]
            last_x = origin + int(last_vehicle["x"] * scale)
            last_y = origin - int(last_vehicle["y"] * scale)
            cv2.putText(img, f"ID {track_id}", (last_x + 10, last_y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

    # 保存图像
    output_path = "all_trajectories.png"
    cv2.imwrite(output_path, img)
    print(f"所有轨迹图已保存到: {output_path}")

# 主函数
def main():
    vehicles_dict = load_all_frames()  # 加载所有车辆的轨迹数据
    plot_all_trajectories(vehicles_dict)  # 绘制并保存轨迹图

if __name__ == "__main__":
    main()