import numpy as np

class BicycleModel:
    def __init__(self, initial_state, L):
        """
        Initialize the Bicycle Model.
        :param initial_state: Initial state [x, y, v, θ, δ, a]
        :param L: Vehicle wheelbase
        """
        if len(initial_state) != 6:
            raise ValueError("Initial state must have 6 elements: [x, y, v, θ, δ, a]")
        self.state = np.array(initial_state, dtype=float)
        self.L = L  

    def predict(self, delta_t, num_steps):
        """
        Perform extrapolation using the Bicycle Model.
        :param delta_t: Time interval Δt
        :param num_steps: Number of time steps for extrapolation
        :return: Predicted state sequence, including the state at each time step
        """
        if delta_t <= 0:
            raise ValueError("delta_t must be positive")
        states = [self.state.copy()]
        for _ in range(num_steps):
            self._update(delta_t)
            states.append(self.state.copy())
        return np.array(states)

    def _update(self, delta_t):
        """
        Update the state variables.
        :param delta_t: Time interval Δt
        """
        x, y, v, theta, delta, a = self.state

        v_new = v + a * delta_t

        theta_new = theta + (v / self.L) * np.tan(delta) * delta_t

        x_new = x + v * np.cos(theta) * delta_t
        y_new = y + v * np.sin(theta) * delta_t

        self.state = np.array([x_new, y_new, v_new, theta_new, delta, a], dtype=float)


if __name__ == "__main__":

    # [x, y, v, θ, δ, a]
    initial_state = [0, 0, 10, np.pi / 6, np.pi / 18, 1.0]  
    L = 2.5  # 车辆轴距，前轮到后轮的距离
    bike = BicycleModel(initial_state, L)

    delta_t = 0.1  
    num_steps = 10 

    predicted_states = bike.predict(delta_t, num_steps)

    for i, state in enumerate(predicted_states):
        print(f"Step {i}: x={state[0]:.2f}, y={state[1]:.2f}, v={state[2]:.2f}, θ={state[3]:.2f}, δ={state[4]:.2f}, a={state[5]:.2f}")