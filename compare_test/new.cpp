// 行人 Track 的 NMS 过滤主流程
void Existence::NMSTracks_Pedestrian() {

    /*======================================================================
     * 0. 取出 loader 中常用的引用，避免反复函数调用
     *====================================================================*/
    auto &frames          = frame_loader_->GetFrames();          // 所有帧（若后续不用可删除）
    auto &tracks          = track_loader_->GetTracks();          // track_id → TrackPtr
    auto &all_frame_objs  = track_loader_->GetFramesObjects();   // 帧序列 → {track_id → {lidar_id → obj}}

    /*======================================================================
     * 1. 在逐帧 NMS 时保存 “被抑制目标” → “主目标 track_id” 的映射
     *    key  : 被抑制 ALObjectPtr（假设指针唯一标识该目标）
     *    value: 主 Track 的 track_id
     *====================================================================*/
    std::unordered_map<ALObjectPtr, int> matched_main_track_ids; // 用于后续统计

    /*----------------------------------------------------------------------
     * 遍历每一帧，先做帧内的 2D IOU NMS
     *----------------------------------------------------------------------*/
    for (auto &frame_objs : all_frame_objs) {                                 // 遍历每帧
        std::vector<ALObjectPtr> frame_pedestrian_objs;                       // 当前帧中所有行人 obj
        frame_pedestrian_objs.reserve(frame_objs.size());                     // 预分配容量

        /*-------------------- 1.1 收集行人对象 ---------------------------*/
        for (auto &track_obj : frame_objs) {                                  // track_obj : <track_id, map<lidar_id,obj>>
            auto &obj_ptr = track_obj.second[default_lidar_];                 // 取缺省雷达对应的 obj
            if (!obj_ptr || obj_ptr->ignore) {                                // 若为空或标记为忽略
                continue;                                                     // 跳过
            }
            // 非行人同样跳过
            if (kType2Category.at(LidarObjectType(obj_ptr->type)) != Category::PEDESTRIAN)
                continue;

            frame_pedestrian_objs.emplace_back(obj_ptr);                      // 收集进待 NMS 向量
        }

        /*-------------------- 1.2 置信度排序(降序) ------------------------*/
        std::sort(frame_pedestrian_objs.begin(), frame_pedestrian_objs.end(),
                  [](const ALObjectPtr &lhs, const ALObjectPtr &rhs) {
                      return lhs->confidence > rhs->confidence;               // 按置信度降序
                  });

        /*-------------------- 1.3 正式 NMS --------------------------------*/
        std::vector<ALObjectPtr> nms_obstacles;                               // 保留下来的主 obj
        for (auto &sub_obstacle : frame_pedestrian_objs) {                    // 遍历每个待判定 obj
            double best_iou = 0.0;                                            // 当前找到的最大 IOU
            int    best_id  = -1;                                             // 最大 IOU 对应在 nms_obstacles 的下标

            /*---------- (a) 手工标注的 obj 直接作为主目标保留 ----------*/
            if (sub_obstacle->is_manual_obj) {                                // 若为人工标注
                nms_obstacles.push_back(sub_obstacle);                        // 永远保留
                continue;                                                     // 继续下一对象
            }

            /*---------- (b) 与已保留主 obj 比较 IOU --------------------*/
            for (int i = 0; i < static_cast<int>(nms_obstacles.size()); ++i) {
                double cur_iou = get_2d_iou_v2(nms_obstacles[i], sub_obstacle); // 计算 2D IOU
                if (cur_iou > best_iou) {                                     // 若更大
                    best_iou = cur_iou;                                       // 更新最大 IOU
                    best_id  = i;                                             // 记录主 obj 下标
                }
            }

            /*---------- (c) 根据 IOU 阈值决定保留还是抑制 ---------------*/
            if (best_iou < 0.05) {                                            // IOU 很小 → 认为独立
                nms_obstacles.push_back(sub_obstacle);                        // 作为新的主 obj
            } else {                                                          // IOU 较大 → 被抑制
                sub_obstacle->track_nms_discard = true;                       // 标记为被 NMS
                matched_main_track_ids[sub_obstacle] =                        // 记录其对应的主 track_id
                        nms_obstacles[best_id]->track_id;
            }
        }
        /* 如果你希望此时 frame_pedestrian_objs = nms_obstacles，可用 swap;
           这里只做 NMS，不再使用 frame_pedestrian_objs，故可省。                */
    }

    /*======================================================================
     * 2. 遍历所有行人 Track，根据 NMS 结果决定是否整体忽略
     *====================================================================*/
    for (auto &[track_id, track] : tracks) {                                  // 遍历每条 track
        /*---------- 2.0 基础过滤 ----------------------------------------*/
        if (track->has_manual_label)                                continue; // 有人工标注永不忽略
        if (kType2Category.at(LidarObjectType(track->objs[0]->type))
            != Category::PEDESTRIAN)                               continue; // 非行人跳过
        if (track->objs.size() >= 4)                                continue; // 帧数≥4 认为稳定 直接保留

        /*---------- 2.1 统计被 NMS 的帧数 和 主 track 票数 --------------*/
        int nms_discard_cnt = 0;                                              // 被 NMS 的帧数
        std::map<int,int> matched_main_tracks;                                // main_track_id → 次数

        for (const auto &obstacle : track->objs) {                            // 遍历本 track 所有帧 obj
            if (!obstacle->track_nms_discard) continue;                       // 未被 NMS 跳过
            ++nms_discard_cnt;                                                // 计数 +1

            auto it = matched_main_track_ids.find(obstacle);                  // 查该 obj 主 track_id
            if (it != matched_main_track_ids.end()) {                         // 找到
                ++matched_main_tracks[it->second];                            // 给对应主 track 投票
            }
        }

        double invalid_ratio = nms_discard_cnt / double(track->objs.size());  // 被 NMS 的比例
        if (invalid_ratio < 0.6) continue;                                    // <60% 帧被 NMS → 认为没问题

        /*---------- 2.2 找投票最多的主 track -----------------------------*/
        int main_track_id   = -1;                                             // 记录最大票数的主 track_id
        int max_match_cnt   = 0;                                              // 最大票数
        for (auto &[mid, cnt] : matched_main_tracks) {                        // 遍历投票 map
            if (cnt > max_match_cnt) {                                        // 票更多
                max_match_cnt = cnt;                                          // 更新
                main_track_id = mid;                                          // 记录主 track_id
            }
        }
        if (main_track_id == -1 || !tracks.count(main_track_id)) continue;    // 未找到有效主 track

        auto &main_track = tracks[main_track_id];                             // 获得主 track

        if (track->exist_score >= main_track->exist_score) continue;          // 置信度比主 track 高则保留

        /*------------------------------------------------------------------
         * 2.3 “逐帧” 点云数量比较：所有被 NMS 的帧都需满足
         *      (cur_pts < main_pts && cur_pts < 100) 才忽略
         *------------------------------------------------------------------*/
        bool every_frame_less_points = true;                                  // 标志位，初始化为真

        for (const auto &obstacle : track->objs) {                            // 再次遍历本 track
            if (!obstacle->track_nms_discard) continue;                       // 只检查被 NMS 的帧

            int frame_id = obstacle->frame_id;                                // 假设有 frame_id 字段
            ALObjectPtr main_ob_same_frame = nullptr;                         // 主 track 中同帧 obj

            /*-------- 在主 track 中找同一帧 ------------------------------*/
            for (const auto &main_ob : main_track->objs) {                    // 遍历主 track
                if (main_ob->frame_id == frame_id) {                          // 同帧
                    main_ob_same_frame = main_ob;
                    break;
                }
            }

            if (!main_ob_same_frame) {                                        // 主 track 没有对应帧
                every_frame_less_points = false;                              // 条件失败
                break;                                                        // 结束循环
            }

            int cur_pts  = obstacle->lidar_supplement.pointcloud_indexes.size();  // 当前 obj 点数
            int main_pts = main_ob_same_frame->lidar_supplement.pointcloud_indexes.size(); // 主 obj 点数

            if (!(cur_pts < main_pts && cur_pts < 100)) {                     // 若条件不满足
                every_frame_less_points = false;                              // 标志置 false
                break;                                                        // 退出
            }
        }

        /*---------- 2.4 若所有条件满足 → 忽略整个 track ------------------*/
        if (every_frame_less_points) {                                        // 所有被 NMS 帧都点更少
            track->ignore        = true;                                      // 整条 track 设为忽略
            track->ignore_reason = TrackIgnoreReason::IGNORE_TRACK_NMS;       // 记录原因

            /* 打印调试信息，便于离线日志核对 */
            LOG(INFO) << "Pedestrian NMS: Ignore track " << track_id
                      << " (exist_score=" << track->exist_score
                      << " vs " << main_track->exist_score
                      << ", frames=" << track->objs.size()
                      << ", invalid_ratio=" << invalid_ratio << ")";
        }
    }
}