void Existence::NMSTracks_Pedestrian() {
    auto &frames = frame_loader_->GetFrames();
    auto &tracks = track_loader_->GetTracks();
    auto &all_frame_objs = track_loader_->GetFramesObjects();

    // 使用一个外部映射表来存储 sub_obstacle 和匹配的主轨迹 ID
    std::unordered_map<ALObjectPtr, int> matched_main_track_ids;

    for (auto &frame_objs : all_frame_objs) {
        std::vector<ALObjectPtr> frame_pedestrian_objs;
        frame_pedestrian_objs.reserve(frame_objs.size());
        for (auto &track_obj : frame_objs) {
            auto &obj_ptr = track_obj.second[default_lidar_];
            if (nullptr == obj_ptr || obj_ptr->ignore) {
                continue;
            }
            if (kType2Category.at(LidarObjectType(obj_ptr->type)) != Category::PEDESTRIAN) continue;
            frame_pedestrian_objs.emplace_back(obj_ptr);
        }
        
        std::sort(frame_pedestrian_objs.begin(), frame_pedestrian_objs.end(),
                  [](const ALObjectPtr &lhs, const ALObjectPtr &rhs) { return lhs->confidence > rhs->confidence; });

        std::vector<ALObjectPtr> nms_obstacles;
        for (auto &sub_obstacle : frame_pedestrian_objs) {
            double best_iou = 0;
            int best_id = -1;
            if (sub_obstacle->is_manual_obj) {
                nms_obstacles.push_back(sub_obstacle);
                continue;
            }

            for (int i = 0; i < nms_obstacles.size(); i++) {
                auto &main_obstacle = nms_obstacles[i];
                double cur_iou = get_2d_iou_v2(main_obstacle, sub_obstacle);
                if (cur_iou > best_iou) {
                    best_id = i;
                    best_iou = cur_iou;
                }
            }
            
            if (best_iou < 0.05) {
                nms_obstacles.push_back(sub_obstacle);
            } else if (best_iou <= 1.0) {
                sub_obstacle->track_nms_discard = true;
                // 将匹配的主轨迹 ID 存入外部映射表
                matched_main_track_ids[sub_obstacle] = nms_obstacles[best_id]->track_id;
            }
        }

        nms_obstacles.swap(frame_pedestrian_objs);
    }

    for (auto &[track_id, track] : tracks) {
        if (track->has_manual_label) {
            continue;
        }
        if (kType2Category.at(LidarObjectType(track->objs[0]->type)) != Category::PEDESTRIAN) {
            continue;
        }
        
        if (track->objs.size() >= 4) {
            continue;
        }
        
        int nms_discard_count = 0;
        std::map<int, int> matched_main_tracks; 
        
        double invalid_ratio = 0.0;
        for (const auto &obstacle : track->objs) {
            if (obstacle->track_nms_discard) {
                nms_discard_count++;
                // 从外部映射表中获取主轨迹 ID
                int main_track_id = matched_main_track_ids.count(obstacle) ? matched_main_track_ids[obstacle] : -1;
                if (main_track_id > 0) {
                    matched_main_tracks[main_track_id]++;
                }
            }
            invalid_ratio = nms_discard_count / double(track->objs.size());
        }
        
        if (invalid_ratio < 0.6) {
            continue;
        }
        
        int most_matched_main_track = -1;
        int max_match_count = 0;
        for (const auto &[main_track_id, count] : matched_main_tracks) {
            if (count > max_match_count) {
                max_match_count = count;
                most_matched_main_track = main_track_id;
            }
        }
        
        if (most_matched_main_track == -1 || tracks.find(most_matched_main_track) == tracks.end()) {
            continue;
        }
        
        auto &main_track = tracks[most_matched_main_track];
        
        if (track->exist_score >= main_track->exist_score) {
            continue;
        }
        
        int current_track_total_points = 0;
        for (const auto &obstacle : track->objs) {
            current_track_total_points += obstacle->lidar_supplement.pointcloud_indexes.size();
        }
        
        int main_track_total_points = 0;
        for (const auto &obstacle : main_track->objs) {
            main_track_total_points += obstacle->lidar_supplement.pointcloud_indexes.size();
        }
        
        if (current_track_total_points < main_track_total_points && current_track_total_points < 100) {
            track->ignore = true;
            track->ignore_reason = TrackIgnoreReason::IGNORE_TRACK_NMS;
            
            LOG(INFO) << "Pedestrian NMS: Ignore track " << track_id 
                     << " (exist_score=" << track->exist_score << " vs " << main_track->exist_score
                     << ", points=" << current_track_total_points << " vs " << main_track_total_points
                     << ", length=" << track->objs.size() << ")"
                     << "invalid_ratio " << invalid_ratio
                     << "track->objs.size() " << double(track->objs.size());
        }
    }
}