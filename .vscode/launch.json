{
    // 使用 IntelliSense 了解相关属性。 
    // 悬停以查看现有属性的描述。
    // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python: load_match_tool_single",
            "type": "python",
            "request": "launch",
            "program": "./Multi_vehicle_data_roujiche/load_match_tool_single.py",
            "console": "integratedTerminal",
            "pythonPath": "/home/<USER>/anaconda3/envs/python39/bin/python",
            "args": [
                "Multi_vehicle_data_roujiche/auto_label_proto_vel/multi-adfm-line-1748615158-1_JME1562_20250527100000_1748313199_1748313219",
                "/home/<USER>/projects/datasets/data/autolabel_bos_l4/adfm_5.21.0.6/multi-adfm-line-1748615158-1_JME1562_20250527100000_1748313199_1748313219/super_frames/multi-adfm-line-1748615158-1_JME1562_20250527100000_1748313199_1748313219_localization.json",
                "/home/<USER>/projects/datasets/data/autolabel_bos_l4/adfm_5.21.0.6/multi-adfm-line-1748615158-1_JME1567_20250527100000_1748313199_1748313219/super_frames/multi-adfm-line-1748615158-1_JME1567_20250527100000_1748313199_1748313219_localization.json",
            ],
            "env": {
                "PYTHONUNBUFFERED": "1"
            }
        },
        {
            "name": "Python: load_match_tool_multi",
            "type": "python",
            "request": "launch",
            "program": "./Multi_vehicle_data_roujiche/load_match_tool_multi.py",
            "console": "integratedTerminal",
            "pythonPath": "/home/<USER>/anaconda3/envs/python39/bin/python",
            "args": [
                "Multi_vehicle_data_roujiche/rt6_wuhan_cutin_flow_prototxt",
                "Multi_vehicle_data_roujiche/localization_gt_json",
            ],
            "env": {
                "PYTHONUNBUFFERED": "1"
            }
        },
        {
            "name": "Python: gen_corr_json",
            "type": "python",
            "request": "launch",
            "program": "./gen_auto_labeling_result/gen_corr_json.py",
            "console": "integratedTerminal",
            "pythonPath": "/home/<USER>/anaconda3/envs/python39/bin/python",
            "env": {
                "PYTHONUNBUFFERED": "1"
            }
        },
        {
            "name": "Python: download_prototxt_multi",
            "type": "python",
            "request": "launch",
            "program": "Multi_vehicle_data_roujiche/download_prototxt_multi.py",
            "console": "integratedTerminal",
            "pythonPath": "/home/<USER>/anaconda3/envs/python39/bin/python",
            "env": {
                "PYTHONUNBUFFERED": "1"
            }
        },
        {
            "name": "Python: gen_auto_label_txt",
            "type": "python",
            "request": "launch",
            "program": "./gen_auto_labeling_result/gen_auto_label_txt.py",
            "console": "integratedTerminal",
            "pythonPath": "/home/<USER>/anaconda3/envs/python39/bin/python",
            "env": {
                "PYTHONUNBUFFERED": "1"
            }
        },
        {
            "name": "Python 调试程序: 当前文件",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal"
        }
    ]
}